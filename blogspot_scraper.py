#!/usr/bin/env python3
"""
Blogspot XPath Scraper
A comprehensive scraper for extracting data from Blogspot blogs using XPath expressions.
"""

import requests
from lxml import html, etree
import json
import re
from urllib.parse import urljoin, urlparse
from typing import Dict, List, Optional, Union
import time


class BlogspotScraper:
    """
    A comprehensive scraper for Blogspot blogs using XPath expressions.
    """
    
    def __init__(self, base_url: str, delay: float = 1.0):
        """
        Initialize the scraper.
        
        Args:
            base_url: The base URL of the Blogspot blog
            delay: Delay between requests in seconds
        """
        self.base_url = base_url.rstrip('/')
        self.delay = delay
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def get_page(self, url: str) -> Optional[html.HtmlElement]:
        """
        Fetch and parse a web page.
        
        Args:
            url: URL to fetch
            
        Returns:
            Parsed HTML element or None if failed
        """
        try:
            if self.delay > 0:
                time.sleep(self.delay)
            
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            # Parse HTML
            tree = html.fromstring(response.content)
            return tree
            
        except Exception as e:
            print(f"Error fetching {url}: {e}")
            return None
    
    def extract_blog_info(self, tree: html.HtmlElement) -> Dict:
        """
        Extract basic blog information.
        
        Args:
            tree: Parsed HTML tree
            
        Returns:
            Dictionary containing blog information
        """
        blog_info = {}
        
        # Blog title - try multiple selectors
        title_xpaths = [
            "//h1[@class='title']/a/span[@itemprop='name']/text()",
            "//h2[@class='title']/a/span[@itemprop='name']/text()",
            "//title/text()",
            "//h1//text()",
            "//h2//text()"
        ]
        
        blog_info['title'] = self._extract_first_match(tree, title_xpaths)
        
        # Blog description
        desc_xpaths = [
            "//p[@class='description']/span/text()",
            "//p[@class='description']//text()",
            "//meta[@name='description']/@content"
        ]
        
        blog_info['description'] = self._extract_first_match(tree, desc_xpaths)
        
        # Blog URL
        url_xpaths = [
            "//h1[@class='title']/a/@href",
            "//h2[@class='title']/a/@href",
            "//link[@rel='canonical']/@href"
        ]
        
        blog_info['url'] = self._extract_first_match(tree, url_xpaths)
        
        return blog_info
    
    def extract_post_data(self, tree: html.HtmlElement) -> Dict:
        """
        Extract post data from a single post page.
        
        Args:
            tree: Parsed HTML tree
            
        Returns:
            Dictionary containing post data
        """
        post_data = {}
        
        # Post title
        title_xpaths = [
            "//h1[@class='post-title entry-title'][@itemprop='name']/text()",
            "//h1[@class='post-title']/text()",
            "//h3[@class='post-title']/a/text()",
            "//h1//text()",
            "//title/text()"
        ]
        
        post_data['title'] = self._extract_first_match(tree, title_xpaths)
        
        # Post URL
        url_xpaths = [
            "//h1[@class='post-title entry-title']/following-sibling::div//a[@class='timestamp-link']/@href",
            "//h3[@class='post-title']/a/@href",
            "//link[@rel='canonical']/@href"
        ]
        
        post_data['url'] = self._extract_first_match(tree, url_xpaths)
        
        # Post content
        content_xpaths = [
            "//div[@class='post-body entry-content']//text()",
            "//div[@class='post-body']//text()",
            "//div[contains(@class, 'post-content')]//text()"
        ]
        
        content_texts = self._extract_all_matches(tree, content_xpaths)
        post_data['content'] = ' '.join([t.strip() for t in content_texts if t.strip()])
        
        # Post date
        date_xpaths = [
            "//span[@class='published'][@itemprop='datePublished']/@title",
            "//span[@class='published']/text()",
            "//abbr[@class='published']/@title",
            "//time/@datetime"
        ]
        
        post_data['date'] = self._extract_first_match(tree, date_xpaths)
        
        # Post author
        author_xpaths = [
            "//span[@class='fn'][@itemprop='author']//span[@itemprop='name']/text()",
            "//span[@class='post-author vcard']//span[@itemprop='name']/text()",
            "//span[@class='author']//text()"
        ]
        
        post_data['author'] = self._extract_first_match(tree, author_xpaths)
        
        # Post labels/tags
        labels_xpaths = [
            "//span[@class='post-labels']//a/text()",
            "//a[@rel='tag']/text()",
            "//div[@class='labels']//a/text()"
        ]
        
        post_data['labels'] = self._extract_all_matches(tree, labels_xpaths)
        
        # Post image
        image_xpaths = [
            "//meta[@itemprop='image_url']/@content",
            "//div[@class='post-body']//img[1]/@src",
            "//meta[@property='og:image']/@content"
        ]
        
        post_data['image'] = self._extract_first_match(tree, image_xpaths)
        
        return post_data
    
    def extract_posts_list(self, tree: html.HtmlElement) -> List[Dict]:
        """
        Extract list of posts from blog homepage or archive page.
        
        Args:
            tree: Parsed HTML tree
            
        Returns:
            List of dictionaries containing post data
        """
        posts = []
        
        # Find all post containers
        post_containers = tree.xpath("//div[contains(@class, 'post')]")
        
        for container in post_containers:
            post = {}
            
            # Title
            title_xpaths = [
                ".//h3[@class='post-title']/a/text()",
                ".//h2[@class='post-title']/a/text()",
                ".//h1[@class='post-title']/text()",
                ".//a[contains(@class, 'title')]//text()"
            ]
            
            post['title'] = self._extract_first_match(container, title_xpaths)
            
            # URL
            url_xpaths = [
                ".//h3[@class='post-title']/a/@href",
                ".//h2[@class='post-title']/a/@href",
                ".//a[@class='timestamp-link']/@href"
            ]
            
            post['url'] = self._extract_first_match(container, url_xpaths)
            
            # Excerpt
            excerpt_xpaths = [
                ".//div[@class='post-body']//text()",
                ".//div[contains(@class, 'excerpt')]//text()",
                ".//p//text()"
            ]
            
            excerpt_texts = self._extract_all_matches(container, excerpt_xpaths)
            excerpt = ' '.join([t.strip() for t in excerpt_texts if t.strip()])
            post['excerpt'] = excerpt[:200] + '...' if len(excerpt) > 200 else excerpt
            
            # Date
            date_xpaths = [
                ".//span[@class='published']/text()",
                ".//abbr[@class='published']/@title",
                ".//time/@datetime"
            ]
            
            post['date'] = self._extract_first_match(container, date_xpaths)
            
            if post.get('title'):  # Only add if we found a title
                posts.append(post)
        
        return posts
    
    def extract_sidebar_widgets(self, tree: html.HtmlElement) -> Dict:
        """
        Extract sidebar widget data.
        
        Args:
            tree: Parsed HTML tree
            
        Returns:
            Dictionary containing widget data
        """
        widgets = {}
        
        # Popular posts
        popular_posts = []
        popular_containers = tree.xpath("//div[@class='PopularPosts']//li")
        
        for container in popular_containers:
            post = {
                'title': self._extract_first_match(container, [".//a/text()"]),
                'url': self._extract_first_match(container, [".//a/@href"]),
                'image': self._extract_first_match(container, [".//img/@src"])
            }
            if post['title']:
                popular_posts.append(post)
        
        widgets['popular_posts'] = popular_posts
        
        # Archive links
        archive_links = []
        archive_containers = tree.xpath("//div[@class='BlogArchive']//li/a")
        
        for link in archive_containers:
            archive_links.append({
                'text': link.text_content().strip(),
                'url': link.get('href')
            })
        
        widgets['archive_links'] = archive_links
        
        # Labels/Categories
        labels = []
        label_containers = tree.xpath("//div[@class='Label']//li/a")
        
        for link in label_containers:
            labels.append({
                'text': link.text_content().strip(),
                'url': link.get('href')
            })
        
        widgets['labels'] = labels
        
        return widgets
    
    def _extract_first_match(self, tree: html.HtmlElement, xpaths: List[str]) -> Optional[str]:
        """
        Extract the first matching result from multiple XPath expressions.
        
        Args:
            tree: HTML tree to search
            xpaths: List of XPath expressions to try
            
        Returns:
            First matching text or None
        """
        for xpath in xpaths:
            try:
                results = tree.xpath(xpath)
                if results:
                    result = results[0]
                    if isinstance(result, str):
                        return result.strip()
                    elif hasattr(result, 'text_content'):
                        return result.text_content().strip()
                    else:
                        return str(result).strip()
            except Exception:
                continue
        return None
    
    def _extract_all_matches(self, tree: html.HtmlElement, xpaths: List[str]) -> List[str]:
        """
        Extract all matching results from multiple XPath expressions.
        
        Args:
            tree: HTML tree to search
            xpaths: List of XPath expressions to try
            
        Returns:
            List of matching texts
        """
        all_results = []
        for xpath in xpaths:
            try:
                results = tree.xpath(xpath)
                for result in results:
                    if isinstance(result, str):
                        text = result.strip()
                    elif hasattr(result, 'text_content'):
                        text = result.text_content().strip()
                    else:
                        text = str(result).strip()
                    
                    if text and text not in all_results:
                        all_results.append(text)
            except Exception:
                continue
        return all_results
    
    def scrape_blog(self, max_pages: int = 5) -> Dict:
        """
        Scrape entire blog data.
        
        Args:
            max_pages: Maximum number of pages to scrape
            
        Returns:
            Complete blog data
        """
        print(f"Starting to scrape blog: {self.base_url}")
        
        # Get homepage
        tree = self.get_page(self.base_url)
        if not tree:
            return {}
        
        # Extract blog info
        blog_data = self.extract_blog_info(tree)
        print(f"Blog title: {blog_data.get('title', 'Unknown')}")
        
        # Extract posts from homepage
        posts = self.extract_posts_list(tree)
        print(f"Found {len(posts)} posts on homepage")
        
        # Extract sidebar widgets
        widgets = self.extract_sidebar_widgets(tree)
        
        blog_data.update({
            'posts': posts,
            'widgets': widgets,
            'scraped_pages': 1
        })
        
        return blog_data


def main():
    """
    Example usage of the BlogspotScraper.
    """
    # Example URLs from the analyzed files
    blogs = [
        "https://niemtin007.blogspot.com",
        "https://nhasachtinhoc.blogspot.com"
    ]
    
    for blog_url in blogs:
        print(f"\n{'='*50}")
        print(f"Scraping: {blog_url}")
        print('='*50)
        
        scraper = BlogspotScraper(blog_url, delay=1.0)
        blog_data = scraper.scrape_blog(max_pages=1)
        
        if blog_data:
            print(f"\nBlog Data Summary:")
            print(f"Title: {blog_data.get('title', 'N/A')}")
            print(f"Description: {blog_data.get('description', 'N/A')[:100]}...")
            print(f"Posts found: {len(blog_data.get('posts', []))}")
            print(f"Popular posts: {len(blog_data.get('widgets', {}).get('popular_posts', []))}")
            print(f"Archive links: {len(blog_data.get('widgets', {}).get('archive_links', []))}")
            
            # Save to JSON file
            filename = f"{urlparse(blog_url).netloc.replace('.', '_')}_data.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(blog_data, f, indent=2, ensure_ascii=False)
            print(f"Data saved to: {filename}")
        else:
            print("Failed to scrape blog data")


if __name__ == "__main__":
    main()
