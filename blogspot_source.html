
<!DOCTYPE html>
<html dir='ltr' xmlns='https://www.w3.org/1999/xhtml' xmlns:b='https://www.google.com/2005/gml/b' xmlns:data='https://www.google.com/2005/gml/data' xmlns:expr='https://www.google.com/2005/gml/expr'>
<head>
<link href='https://www.blogger.com/static/v1/widgets/55013136-widget_css_bundle.css' rel='stylesheet' type='text/css'/>
<meta content='text/html; charset=UTF-8' http-equiv='Content-Type'/>
<meta content='blogger' name='generator'/>
<link href='https://niemtin007.blogspot.com/favicon.ico' rel='icon' type='image/x-icon'/>
<link href='https://niemtin007.blogspot.com/2016/06/how-to-make-menu-multios-from-clover-custom-entries-windows-macos-linux.html' rel='canonical'/>
<link rel="alternate" type="application/atom+xml" title="UEFI &amp; OS - Atom" href="https://niemtin007.blogspot.com/feeds/posts/default" />
<link rel="alternate" type="application/rss+xml" title="UEFI &amp; OS - RSS" href="https://niemtin007.blogspot.com/feeds/posts/default?alt=rss" />
<link rel="service.post" type="application/atom+xml" title="UEFI &amp; OS - Atom" href="https://www.blogger.com/feeds/6617778967066801638/posts/default" />

<link rel="alternate" type="application/atom+xml" title="UEFI &amp; OS - Atom" href="https://niemtin007.blogspot.com/feeds/*******************/comments/default" />
<!--Can't find substitution for tag [blog.ieCssRetrofitLinks]-->
<link href='https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEhg3SBOwUohbv8QNGG2PoNzENttV9JqyXiKxd0RCQq7smauS3OG17ZGIoYutDo5Q8x3qg7xJdeRxVRDyxLyo0hHv_eNzmZWm1DSBZWEMGQGCLnVMxEYNFWL7C6qp3VhjLznmFkzEpmVvxk/s1600/clover.png' rel='image_src'/>
<meta content='Custom Entries, Multiboot, Linux, Windows, Mac OS' name='description'/>
<meta content='https://niemtin007.blogspot.com/2016/06/how-to-make-menu-multios-from-clover-custom-entries-windows-macos-linux.html' property='og:url'/>
<meta content='[Guide] Sử dụng Clover bootloader để quản lý Multi OS' property='og:title'/>
<meta content='Custom Entries, Multiboot, Linux, Windows, Mac OS' property='og:description'/>
<meta content='https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEhg3SBOwUohbv8QNGG2PoNzENttV9JqyXiKxd0RCQq7smauS3OG17ZGIoYutDo5Q8x3qg7xJdeRxVRDyxLyo0hHv_eNzmZWm1DSBZWEMGQGCLnVMxEYNFWL7C6qp3VhjLznmFkzEpmVvxk/w1200-h630-p-k-no-nu/clover.png' property='og:image'/>
<title>[Guide] Sử dụng Clover bootloader để quản lý Multi OS - UEFI &amp; OS</title>
<!-- [ Social Media meta tag ] -->
<meta content='article' property='og:type'/>
<meta content='https://niemtin007.blogspot.com/2016/06/how-to-make-menu-multios-from-clover-custom-entries-windows-macos-linux.html' property='og:url'/>
<meta content='https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEhg3SBOwUohbv8QNGG2PoNzENttV9JqyXiKxd0RCQq7smauS3OG17ZGIoYutDo5Q8x3qg7xJdeRxVRDyxLyo0hHv_eNzmZWm1DSBZWEMGQGCLnVMxEYNFWL7C6qp3VhjLznmFkzEpmVvxk/s1600/clover.png' property='og:image'/>
<meta content='Custom Entries, Multiboot, Linux, Windows, Mac OS' property='og:description'/>
<meta content='UEFI &amp; OS' property='og:site_name'/>
<meta content='en_US' property='og:locale'/>
<!-- Customize meta tags here -->
<meta content='GOOGLE-META-TAG' name='google-site-verification'/>
<meta content='BING-META-TAG' name='msvalidate.01'/>
<meta content='ALEXA-META-TAG' name='alexaVerifyID'/>
<meta content='width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1' name='viewport'/>
<link href='//maxcdn.bootstrapcdn.com/font-awesome/4.3.0/css/font-awesome.min.css' rel='stylesheet'/>
<script src='https://ajax.googleapis.com/ajax/libs/jquery/1.7.1/jquery.min.js' type='text/javascript'></script>
<link href='https://fonts.googleapis.com/css?family=Open+Sans%3A400%2C400italic%2C700%2C300%2C80&ver=4.3' rel='stylesheet' type='text/css'/>
<link href='https://fonts.googleapis.com/css?family=Oswald:300' rel='stylesheet' type='text/css'/>
<link href='https://fonts.googleapis.com/css?family=Roboto+Condensed' rel='stylesheet' type='text/css'/>
<style id='page-skin-1' type='text/css'><!--
/*-----------------------------------------------
Platform: Blogger
Name:     Clean Shift Blogger Template
Designer: Bloggertheme9
URL:      https://www.bloggertheme9.com
License: Free Version
----------------------------------------------- */
/* Variable definitions
====================
<Variable name="keycolor" description="Main Color" type="color" default="#1a222a"/>
<Variable name="body.background" description="Body Background" type="background"
color="#3589c3" default="#111111 url(//themes.googleusercontent.com/image?id=1OACCYOE0-eoTRTfsBuX1NMN9nz599ufI1Jh0CggPFA_sK80AGkIr8pLtYRpNUKPmwtEa) repeat-x fixed top center"/>
<Group description="Page Text" selector="body">
<Variable name="body.font" description="Font" type="font"
default="normal normal 15px Arial, Tahoma, Helvetica, FreeSans, sans-serif"/>
<Variable name="body.text.color" description="Text Color" type="color" default="#333333"/>
</Group>
<Group description="Backgrounds" selector=".body-fauxcolumns-outer">
<Variable name="body.background.color" description="Outer Background" type="color" default="#296695"/>
<Variable name="header.background.color" description="Header Background" type="color" default="transparent"/>
<Variable name="post.background.color" description="Post Background" type="color" default="#ffffff"/>
</Group>
<Group description="Links" selector=".main-outer">
<Variable name="link.color" description="Link Color" type="color" default="#336699"/>
<Variable name="link.visited.color" description="Visited Color" type="color" default="#6699cc"/>
<Variable name="link.hover.color" description="Hover Color" type="color" default="#33aaff"/>
</Group>
<Group description="Blog Title" selector=".header h1">
<Variable name="header.font" description="Title Font" type="font"
default="normal normal 36px Arial, Tahoma, Helvetica, FreeSans, sans-serif"/>
<Variable name="header.text.color" description="Text Color" type="color" default="#ffffff" />
</Group>
<Group description="Tabs Text" selector=".tabs-inner .widget li a">
<Variable name="tabs.font" description="Font" type="font"
default="normal normal 15px Arial, Tahoma, Helvetica, FreeSans, sans-serif"/>
<Variable name="tabs.text.color" description="Text Color" type="color" default="#ffffff"/>
<Variable name="tabs.selected.text.color" description="Selected Color" type="color" default="#4183bf"/>
</Group>
<Group description="Tabs Background" selector=".tabs-outer .PageList">
<Variable name="tabs.background.color" description="Background Color" type="color" default="transparent"/>
<Variable name="tabs.selected.background.color" description="Selected Color" type="color" default="transparent"/>
<Variable name="tabs.separator.color" description="Separator Color" type="color" default="transparent"/>
</Group>
<Group description="Post Title" selector="h3.post-title, .comments h4">
<Variable name="post.title.font" description="Title Font" type="font"
default="normal normal 18px Arial, Tahoma, Helvetica, FreeSans, sans-serif"/>
</Group>
<Group description="Date Header" selector=".date-header">
<Variable name="date.header.color" description="Text Color" type="color" default="#6a6a6a"/>
</Group>
<Group description="Post" selector=".post">
<Variable name="post.footer.text.color" description="Footer Text Color" type="color" default="#999999"/>
<Variable name="post.border.color" description="Border Color" type="color" default="#dddddd"/>
</Group>
<Group description="Gadgets" selector="h2">
<Variable name="widget.title.font" description="Title Font" type="font"
default="bold normal 13px Arial, Tahoma, Helvetica, FreeSans, sans-serif"/>
<Variable name="widget.title.text.color" description="Title Color" type="color" default="#888888"/>
</Group>
<Group description="Footer" selector=".footer-outer">
<Variable name="footer.text.color" description="Text Color" type="color" default="#cccccc"/>
<Variable name="footer.widget.title.text.color" description="Gadget Title Color" type="color" default="#aaaaaa"/>
</Group>
<Group description="Footer Links" selector=".footer-outer">
<Variable name="footer.link.color" description="Link Color" type="color" default="#99ccee"/>
<Variable name="footer.link.visited.color" description="Visited Color" type="color" default="#77aaee"/>
<Variable name="footer.link.hover.color" description="Hover Color" type="color" default="#33aaff"/>
</Group>
<Variable name="content.margin" description="Content Margin Top" type="length" default="20px" min="0" max="100px"/>
<Variable name="content.padding" description="Content Padding" type="length" default="0" min="0" max="100px"/>
<Variable name="content.background" description="Content Background" type="background"
default="transparent none repeat scroll top left"/>
<Variable name="content.border.radius" description="Content Border Radius" type="length" default="0" min="0" max="100px"/>
<Variable name="content.shadow.spread" description="Content Shadow Spread" type="length" default="0" min="0" max="100px"/>
<Variable name="header.padding" description="Header Padding" type="length" default="0" min="0" max="100px"/>
<Variable name="header.background.gradient" description="Header Gradient" type="url"
default="none"/>
<Variable name="header.border.radius" description="Header Border Radius" type="length" default="0" min="0" max="100px"/>
<Variable name="main.border.radius.top" description="Main Border Radius" type="length" default="20px" min="0" max="100px"/>
<Variable name="footer.border.radius.top" description="Footer Border Radius Top" type="length" default="0" min="0" max="100px"/>
<Variable name="footer.border.radius.bottom" description="Footer Border Radius Bottom" type="length" default="20px" min="0" max="100px"/>
<Variable name="region.shadow.spread" description="Main and Footer Shadow Spread" type="length" default="3px" min="0" max="100px"/>
<Variable name="region.shadow.offset" description="Main and Footer Shadow Offset" type="length" default="1px" min="-50px" max="50px"/>
<Variable name="tabs.background.gradient" description="Tab Background Gradient" type="url" default="none"/>
<Variable name="tab.selected.background.gradient" description="Selected Tab Background" type="url"
default="url(//www.blogblog.com/1kt/transparent/white80.png)"/>
<Variable name="tab.background" description="Tab Background" type="background"
default="transparent url(//www.blogblog.com/1kt/transparent/black50.png) repeat scroll top left"/>
<Variable name="tab.border.radius" description="Tab Border Radius" type="length" default="10px" min="0" max="100px"/>
<Variable name="tab.first.border.radius" description="First Tab Border Radius" type="length" default="10px" min="0" max="100px"/>
<Variable name="tabs.border.radius" description="Tabs Border Radius" type="length" default="0" min="0" max="100px"/>
<Variable name="tabs.spacing" description="Tab Spacing" type="length" default=".25em" min="0" max="10em"/>
<Variable name="tabs.margin.bottom" description="Tab Margin Bottom" type="length" default="0" min="0" max="100px"/>
<Variable name="tabs.margin.sides" description="Tab Margin Sides" type="length" default="20px" min="0" max="100px"/>
<Variable name="main.background" description="Main Background" type="background"
default="transparent url(//www.blogblog.com/1kt/transparent/white80.png) repeat scroll top left"/>
<Variable name="main.padding.sides" description="Main Padding Sides" type="length" default="20px" min="0" max="100px"/>
<Variable name="footer.background" description="Footer Background" type="background"
default="transparent url(//www.blogblog.com/1kt/transparent/black50.png) repeat scroll top left"/>
<Variable name="post.margin.sides" description="Post Margin Sides" type="length" default="-20px" min="-50px" max="50px"/>
<Variable name="post.border.radius" description="Post Border Radius" type="length" default="5px" min="0" max="100px"/>
<Variable name="widget.title.text.transform" description="Widget Title Text Transform" type="string" default="uppercase"/>
<Variable name="mobile.background.overlay" description="Mobile Background Overlay" type="string"
default="transparent none repeat scroll top left"/>
<Variable name="startSide" description="Side where text starts in blog language" type="automatic" default="left"/>
<Variable name="endSide" description="Side where text ends in blog language" type="automatic" default="right"/>
*/
/* Content
----------------------------------------------- */
html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code,
del, dfn, em, font, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, dl, dt, dd, ol, ul, li, fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td, figure { margin: 0; padding: 0;}
article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section {display:block;}
table {border-collapse: separate; border-spacing: 0;}
caption, th, td {text-align: left; font-weight: normal;}
table{margin:15px 0; border: 1px solid #eee; border-right:0; border-top:0; width: 100%;}
tr th{background:#F2F2F2; width:18em; border-right: 1px solid #e6e6e6;}
tr{text-indent:10px; display:block; padding:8px 0; border-top: 1px solid #eee;}
tr td{font-size:13px; width:18em; border-right: 1px solid #eee;}
tr:nth-child(2n+1) {background: #f4f4f4;}
blockquote:before, blockquote:after,
q:before, q:after {content: "";}
blockquote, q {quotes: "" "";}
sup{ vertical-align: super; font-size:smaller; }
code{ font-family: 'Courier New', Courier, monospace; font-size:12px; color:#272727; }
a img{border: none;}
ul ul, ol ol { padding: 0; }
ol, ul { padding: 0px;  margin: 0; }
ol li { list-style-type: none;  padding:0;  }
ul li { list-style-type: none;  padding: 0;  }
h1, h2, h3, h4, h5, h6 {color: #444; font-weight:500;}
a{ color: #4183bf; outline:none; text-decoration: none; }
a:hover { color: #4fb9ff; text-decoration:none; }
body{ background: #eee; color: #666; height: 100%; padding: 0; font:14px/22px "Open Sans",Arial,Tahoma,sans-serif;}
.clear { clear:both; float:none; }
.ct-wrapper {padding:0px 0px; position:relative; margin: 0px;}
.outer-wrapper { position: relative; padding:0px 0 }
.header-wrapper {display: inline-block; float: left; padding: 0; width: 100%; -moz-box-sizing: -webkit-border-box; box-sizing: border-box; }
.main-wrapper { width:auto; margin-right:355px; }
#content { box-sizing: border-box; -moz-box-sizing: border-box; -webkit-box-sizing: border-box; position: relative;}
.main-inner-wrap {float:left; position: relative; width:100%;}
.sidebar-wrapper { width:336px; float: right;}
.container {margin: 0 auto; padding: 0 0px; position: relative; max-width: 1100px;}
body#layout #top-nav { margin-top: 40px; }
body#layout #header, body#layout .header-right { width: 46%; }
body#layout .main-wrapper { margin-right: 350px; }
body#layout .widget-content { margin: 0; }
body#layout .footer {width:19.3%;}body#layout .sub-dd{margin-bottom:0; padding:10px;}
body#layout .outer-wrapper, body#layout .sidebar-wrapper, body#layout .ct-wrapper { margin: 0; padding: 0; }
.ct-wrapper, .crosscol, .post, .sidebar-wrapper, .buzed{overflow:hidden;}
#header{ float:left; width: 23%; }
#header-inner{ margin:10px 0 8px 7%; padding: 0; }
#header h1, #header h2 { font-size: 26px; font-varient: small-caps; line-height: 30px;}
#header h1 a, #header h2 a{ color:#3589c3; }
#header h1 a:hover,#header h2 a:hover { color:#555; }
#header h1 a:beforee, #header h2 a:beforee{font-family:'FontAwesome'; content:"\f132"; font-size: 21px;}
#header p.description{color:#444; font-size: 14px; letter-spacing: 0.8px; text-transform: capitalize; disply: inline-block; }
#header img{ border:0 none; background:none; height:auto;}
.lefter{margin-left:10px; margin-right:10px;}
.header-right { float: right; }
.header-right .widget-content { margin: 12px 0px 5px; }
.item-control a img{}
.top{padding-top:25px;}
#navigation.fixed-nav{position: fixed; top: 0; left: 0; width: 100% !important; z-index: 9999999; padding: 0; opacity: 0.95; filter: alpha(opacity=95); box-shadow:0 6px 8px -1px rgba(0, 0, 0, 0.2);}
#peekar{position:relative; width:auto; background:#fff; border:1px solid #dfdfdf; padding:16px 15px; margin:0 0 18px; }
#peekar input{font-size:12px; margin: 0px 0 0; padding: 10px 0px; text-indent:7px; width:100%; color:#c5c5c5; border:1px solid #ccc; -o-transition:width .7s,color .4s;-webkit-transition:width .7s,color .4s;-moz-transition:width .7s,color .4s;transition:width .7s,color .4s;-webkit-border-radius:0;-moz-border-radius:0;border-radius:0}
#peekar input:focus{}
#peekar button{border: 0; background:none; float:right; cursor: pointer; color:#ccc; position:absolute; right:16px; top:20px; font-size:18px;}
#peekar button:hover{}
.top-nav{background:#F4F4F4; overflow:hidden; width: 100%; border-bottom:1px solid #ededed;}
.Pagemenu {display: block; float:left; }
.Pagemenu li {display: inline-block; position: relative; border-right:1px solid #7b7777; margin: 0 8px 0 0; padding: 0 8px 0 0;}
.Pagemenu li a {text-decoration: none; line-height:14px; display: block; color: #6f6b6b; transition: all 0.2s ease-in-out 0s;}
.Pagemenu li:last-child{border-right:0; padding: 0 0;}
.Pagemenu li a:hover,.Pagemenu li:hover>a, .Pagemenu li a.home {color:#6f6b6b;}
.backer {position:relative; background:#FFFFFF; margin:0 0 0px; display: block; width:100%; float:left; }
.menu {display: block; float:right; position:relative;}
.menu li {display: inline-block; margin:0 22px 0 0; position: relative; z-index: 10;}
.menu li:first-child {margin-left: 0;}
.menu li a {font-size: 13px; line-height:68px; text-transform: uppercase; text-decoration: none; display: block; color: #6f6b6b; transition: all 0.2s ease-in-out 0s;}
.menu li a:hover,.menu li:hover>a {border-top:2px solid #3589c3;}
.menu .home a, .menu .home a:hover{border-top:2px solid #3589c3;}
.menu ul {visibility: hidden; opacity: 0; margin: 0; padding: 0; width: 150px; position: absolute; left: 0px;  background: #363b3f; z-index: 9;    transform: translate(0,20px); transition: all 0.2s ease-out;}
.menu ul li {display: block; float: none; background: none; margin: 0; padding: 0;}
.menu ul li a {color: #ddd; display: block; line-height:50px; padding:0 14px; font-weight: normal;}
.menu ul li a:hover,.menu ul li:hover>a {background: #3589c3; color: #fff; border-top:none;}
.menu li:hover>ul {visibility: visible; opacity: 1; transform: translate(0,0);}
.menu ul ul {left: 149px; top: 0px; visibility: hidden; opacity: 0; transform: translate(20px,20px); transition: all 0.2s ease-out;}
.menu ul ul:after {left: -6px; top: 10%; border: solid transparent; content: " "; height: 0; width: 0; position: absolute; pointer-events: none; border-color: rgba(255, 255, 255, 0); border-right-color: #EBEBEB; border-width: 6px; margin-top: -6px;}
.menu li>ul ul:hover {visibility: visible; opacity: 1; transform: translate(0,0);}
.resp-desk,.resp-desk1 {display: none; text-transform: uppercase; font-weight: 500;}
.resp-desk {padding:20px 10px;}
.resp-desk a{float:right;}
.resp-desk1 {padding: 12px 6px;}
.resp-desk a, .resp-desk1 a{color: #444;}
.social-ico{float:right; display:inline; margin:2.5px 0; overflow:hidden;}
.social-ico a{background: #fff; border: 1px solid #e9e9e9; border-radius: 14px; color: #666; float: left; font-size: 11px; margin: 0 8px 0 0; opacity: 0.9; padding: 2px 8px;}
.social-ico a:hover{opacity:1}
.social-ico a.tt1 i{background:#3B5998;}
.social-ico a.tt2 i{background:#D64136;}
.social-ico a.tt3 i{background:#19BFE5;}
.social-ico a.tt4 i{background:#D88B43;}
.social-ico a i{border-radius: 50%; color: #fff; height: 16px; line-height: 16px; text-align: center; width: 16px;}
a.homer {}
.omage {background: #43494C; float:left; height:78px;width: 100%;}
.header-phone:before{ border: 1px solid #fff; border-radius: 50%; color: #fff; font-family: FontAwesome; font-size: 24px; left: 25px; height:40px; width:40px; text-align:center; line-height:40px; position: absolute; top: 25px; content:"\f095";}
.header-phone {background: #3589c3; float: left; height: 56px; padding: 20px 0 20px 75px; position: relative; width: 291px;}
.header-phone h2, .header-phone h4 {color: #fff; font-size: 18px; font-weight: 400; padding-top: 5px;}
.header-phone h4 {font-size: 13px; letter-spacing:.7px; padding-top: 0;}
.triangle-arrow {border-right: 58px solid transparent; border-top: 96px solid #3589c3; display: block; height: 0; position: absolute; right: -58px; top: 0; width: 0;}
.header-tagline {float: right; padding: 8px 0; text-align: center; width: 55%; }
.header-tagline h4{color:#fff; font-size: 18px; font-style:italic; letter-spacing:.8px; font-weight: 300; line-height: 56px;}
.post { margin: 0px 0 0; padding: 0px 0px; }
.post-title {font-size: 23px; color:#444; font-weight: 500; margin: 0px 0 0px; line-height: normal; }
.post-title a {color:#444;}
.post-title a:hover {}
.post-body { padding: 0; margin:0; text-transform: auto; word-wrap:break-word;  }
.post-header {color: #999999; font-size: 12px;}
.nread{font-size:12px; color:#999; border-bottom: 1px solid #eaeaea; margin-bottom: 16px; padding-bottom: 7px;}
.nread a{color:#999;}
a.email-ico{margin-left:10px; float:right; }
a:hover.email-ico{color:#37bc4a !important;}
a:hover.pinterest-ico{color:#d64136 !important;}
a.pinterest-ico{float:right;}
.title-secondary a, .title-secondary{color:#aaa; font-size:13px; color:#909090; margin:3px 0 7px;}
.post-body img {}
.menu ul:after {bottom: 100%; left: 20%; border: solid transparent; content: " "; height: 0; width: 0; position: absolute; pointer-events: none; border-color: rgba(255, 255, 255, 0); border-bottom-color: #363b3f; border-width: 6px; margin-left: -6px;}
.post-meat a{position:absolute; left:8px; top:8px; color:#fff; padding:4px 10px; font-weight:600; background:#3589c3; font-size:13px; text-transform: capitalize;}
.post-meat a:hover{color:#fff !important;}
.tagz a{color: #c62021; font-weight:600; font-size:15px; margin-right: 10px; text-transform: uppercase;}
#breadcrumbs a:hover, .sidebar a:hover, .post-title a:hover, .title-secondary a:hover, .nread a:hover{color: #4183bf;}
.sidebar{font-size: 14px;  margin: 0;  padding: 0;  display: block;  }
.sidebar .widget{background: #fff; border: 1px solid #dfdfdf; padding: 10px 18px; clear: both; margin-bottom: 25px; }
.sidebar ul{ margin:0; padding:0; list-style:none; }
.sidebar li{border-bottom: 1px solid #eee; margin: 0; padding: 7px 0 7px 7px; text-transform: capitalize;}
.sidebar li:last-child, .footer li:last-child{border-bottom:none !important;}
.sidebar li:first-child, .footer li:first-child, .PopularPosts ul li:first-child, #recent-posts li:first-child{padding-top:2px !important;}
.sidebar a{color:#444;}
.v-wrap {position: relative; padding-bottom: 56.25%; height: 0; overflow: hidden; margin:15px 2%;}
.v-wrap iframe, .v-wrap object, .v-wrap embed {position: absolute; top: 0; left: 0; width: 100%; height: 100%;}
.entry-content img, .entry-content iframe, .entry-content object, .entry-content embed {max-width: 100%;}
.in-lefter{margin:0 10px;}
.bondage {background: #34393C; height:78px;width: 100%;}
.bottom-line:before{color: #202528; font-family: FontAwesome; text-shadow:1px 1px #555; font-size: 38px; left: 30px; position: absolute; top: 28px; content:"\f099";}
.bottom-line{color: #777; float: left; font-size: 15px; line-height: 1.7em; padding: 15px 0 0 85px; width: 43%;}
.tride-right {background: #3589c3; float: right; padding: 11px 0 11px 70px; position: relative; margin:-18px 0 0; width: 291px;}
.trig-right {border-left: 58px solid transparent; border-bottom: 96px solid #3589c3; display: block; height: 0; position: absolute; left: -58px; top: 0; width: 0;}
h2.btitle{font-size:22px; line-height: 26px; margin: 0 0 3px; transition: all 0.2s ease-in-out 0s;}
h2.btitle a{color:#606060;}
h2.btitle a:hover{color:#4775A3;}
.blog-cent p{height:;}
.blog-cent{margin: 0px 0 0; padding: 0px 0px;}
.bukshan img{height:100%; width: 100%; transition:all .3s ease-out;}
.bukshan{width:30%; position:relative; height:150px; margin:3px 20px 3px 0; float:left; overflow:hidden;}
.meta-date{color: #aaa; font-size: 13px; font-weight: 500;}
a.button {color: #fff; padding: 7px 14px; margin:8px 0 0; cursor: pointer; display: inline-block; font-size: 13px; font-weight: 700; overflow: hidden; text-transform: uppercase; transition: background-color 0.2s ease-in-out 0s;}
.greden{padding:1.1em 0; margin:20px 0 0; overflow:hidden; background:#fafafa;border:1px solid #e6e6e6; }
.reg-bent{width:50%;float:left;margin:0;text-align:left;color:#bbb;transition:all .3s ease-out;}
.fed-bent{width:50%;float:right;margin:0;text-align:right;color:#bbb;transition:all .3s ease-out}
.fed-bent:hover .two-left,.reg-bent:hover .two-left{color:#ddd!important;}
.reg-bent a,.fed-bent a{color:#999;}
.fed-bent a:hover,.reg-bent a:hover{color:#666!important;}
.reg-bent a,.fed-bent a,.one-left,.one-right{font-size:14px;font-family: &#39;Open Sans&#39;,Helvetica,Arial,sans-serif;font-weight:400;background:none;text-decoration:none}
.one-left{padding:0 0 0 15px;}
.one-right{padding:0 15px 0 0;}
.two-left{font-family: &#39;Open Sans&#39;,sans-serif;font-size:22px;font-weight:700;text-transform:uppercase;transition:all .3s ease-out}
.reg-bent2{margin:0}
.fed-bent2{margin:0}
#blog-pager-newer-link{float:left;padding:0 0 0 20px;}
#blog-pager-older-link{float:right;padding:0 15px 0 0;}
.blog-pager,#blog-pager{clear:both;text-align:center; padding:1em 0;}
.home-link{display:none;}
#MD-StoTop {background:#3589c3; padding:6px 11px; color:#fff; text-align: center; border-radius: 8px; position:fixed; bottom:18px; right:25px; cursor:pointer; }
a:hover#MD-StoTop{opacity:0.8;}
.btf-thumb{display:block; margin:8px 0 0;}
.btf-thumb li{float: right !important; width: 25% !important; margin:0 2.2% 0 0 !important;}
.btf-thumb li a{padding:0 !important; border:0 !important;}
.btf-thumb .teetli a{}
.btf-thumb img{width:100%; opacity:0.8; height:158px; transition:all .3s ease-out;}
.btf-thumb img:hover{opacity:1;}
.boped{float:left;}
.nread{overflow:hidden; margin:8px 0;}
.feet-icons{margin:8px 0 4px;}
.feet-icons a{display:inline-block; font-size: 18px; line-height: 2em; text-align: center; width: 42px;}
a.facebook-ico{color:#3B5998; border-bottom:3px solid #3B5998;}
a.twitter-ico{color:#19BFE5; border-bottom:3px solid #19BFE5;}
a.plus-ico{color:#D64136; border-bottom:3px solid #D64136;}
a.bitz{color:#37BC4A; border-bottom:3px solid #37BC4A;}
.taze {position:relative;}
.taze a{cursor:pointer;}
.jeep {display:none; background:#444; padding:6px 8px; position:absolute; z-index:9; right:0;}
blockquote {border-color: #CCCCCC; border-style: dashed; border-width: 2px 0; color: #888; margin: 10px 0 10px 0; padding:1% 16px 2%;}
blockquote:before{font-family:fontawesome; content:"\f10d"; color:#ddd; float: left; font-size: 34px; font-style: normal; font-weight: normal; margin: 1.6% 14px 0 0;}
.crosscol{text-align:center; margin:0px 0 0px;}
.buzed{text-align:center; }
.footer{width:29%; margin:0 3% 20px 0; padding:0 3% 0px 0; border-right:1px solid #52585b; float:left;}
.gazer{margin-right:0; padding-right:0; border-right:0;}
ul.social-profile-icons { float: right; width: auto; margin: 4px 0 0; }
ul.social-profile-icons li {border: none !important; list-style-type: none !important; margin: 0 !important; padding: 0 !important; }
ul.social-profile-icons li a, ul.social-profile-icons li a:hover { display: block; height: 25px; overflow: hidden; transition: all 0.25s linear 0s; width: 25px; padding: 0; }
#breadcrumbs {margin-bottom:0px; font-size:13px; }
#breadcrumbs ul { margin: 0; padding: 0;}
#breadcrumbs ul li { display: inline-block; position:relative; margin: 0 0 0 18px;}
#breadcrumbs a{color:#768187; margin:0; }
#breadcrumbs ul li:first-child a{margin:0px;}
#breadcrumbs ul li::before {border-color: transparent transparent transparent #aaa; border-image: none; border-style: solid; border-width: 4px; content: ""; height: 0; left: -12px; position: absolute; top: 8px; width: 0;}
#breadcrumbs ul li:first-child::before{border:none;}
.seat-bottom{margin-bottom:10px;}
.PopularPosts li,.PopularPosts li img,.PopularPosts li a,.PopularPosts li a img{margin:0;padding:0;list-style:none;background:none;outline:none}
.PopularPosts ul{margin:0;list-style:none;}
.PopularPosts ul li img{margin:0 10px 0 0; width:75px; height:60px; overflow:hidden; float:left; transition:all .3s ease-out}
.PopularPosts ul li{position:relative;margin:0; padding:13px 0 !important;position:relative;}
.PopularPosts ul li:last-child, #recent-posts li:last-child{border-bottom:none}
.PopularPosts ul li .item-title a,.PopularPosts ul li a{font-weight: normal;}
.PopularPosts .item-thumbnail{margin:0;}
.PopularPosts .item-snippet{display:none}
.PopularPosts .item-title{padding:0 0px}
#recent-posts img{margin:0 10px 0 0; width:70px; height:55px; overflow:hidden; float:left; transition:all .3s ease-out}
#recent-posts li{position:relative;margin:0; padding:8px 0 !important;position:relative;}
.showpageNum a, .showpage a, .showpagePoint {background:#3589c3; color: #fff; cursor: pointer; font-size: 14px; font-weight: 600; padding: 7px 16px; text-decoration: none; display: inline-block; border-radius:4px; transition:all .5s ease-out}
.showpageNum a:hover, .showpage a:hover, .showpagePoint {background:#3D4145; color: #fff; text-decoration: none; }
.showpageOf {display:none; margin-right:30px; margin-left:8px; }
.showpagePoint, .showpage a, .showpageNum a { margin: 0 8px 0 0; }
.instagram li{float:left; height:90px; list-style:none; width:31.3%; margin:0 !important; padding:0 3px 6px !important;}
.instagram img{height:100%; width:100%; transition:all .3s ease-out;}
.instagram img:hover, .PopularPosts ul li img:hover, .related-post img:hover, #recent-posts img:hover{opacity:0.7; }
#footer { width: 100%; color: #d0d0d0; }
.footer {}
.footer h2 {color: #fff; font-size: 15px; letter-spacing:.8px; margin: 0 0 10px; }
.footer .widget{ clear: both; margin: 0px 0px; }
.footer ul{ margin:0; padding:0; list-style:none; }
.footer li {border-bottom: 1px solid #565656; list-style: none; margin: 0 !important; padding: 8px 8px !important; text-transform: capitalize;}
.mage1{background:#353738; border-bottom:2px solid #444; border-top:4px solid #3589c3; padding:30px 0 25px;}
.footer-credits {border-top: 1px solid #52585b; padding: 20px 0px 0 12px;}
.footer-credits .attribution { font-size:13px; }
#footer a { color:#d0d0d0;  }
#footer a:hover { color: #fff; }
.mage2{}
.deen{float:right;}
.form-go{ border: medium none; box-shadow: none; color: #fff; cursor: pointer; float: right; font-size: 13px; font-weight: 700; height: 40px; line-height: 18px; margin: -40px 0 0; padding: 10px 15px; position: relative; text-transform: uppercase; transition: all 0.2s ease-in-out 0s; z-index: 5;}
.form-bar{background-color: #505050; border: medium none; color: #808080; float: left;    font-size: 13px; font-weight: 600; height: 20px; line-height: 18px; margin-top: 15px; padding: 10px 14px; position: relative;  transition: all 0.2s ease-in-out 0s; width: 85%; z-index: 1;}
#related-article{width:100%;font-size:16px;display:inline-block;margin:auto;padding:8px 0px 0}
#related-article h4{font-size:18px;text-transform:uppercase;margin:0 0 25px;padding-bottom:15px;font-weight:500;letter-spacing:1px;text-align:center;position:relative}
#related-article h4:after{content:'';position:absolute;width:4px;height:4px;background:#aaa;bottom:0;left:47%;box-shadow:1em 0 0 0 #aaa,2em 0 0 0 #aaa}
#related-article ul {padding:0;margin:0;}
#related-article ul li{list-style:none;display:block;float:left;width:31.6%; height:75px; margin:0 12px 18px 0; padding:0; position:relative;overflow:hidden;}
#related-article ul li:last-child{margin-right:0;}
a.related-title{padding:2px 0px; height:57px; overflow:hidden; color:#606060; font-size:13px; line-height:20px; display:block; transition:all .3s;}
#related-article ul li:hover a.related-title{color:#4183bf;}
#related-article img{transition:all .3s ease-out; float:left; width: 90px; height:70px; margin-right:10px;}
#comments{background:#fafafa; clear:both; margin-top:20px; line-height:1em; padding:20px; border:1px solid #e6e6e6; }
.comments .comments-content {margin: 25px 0 10px;}
.comments .comments-content .comment-header{overflow:hidden; width:100%; margin:0;}
.datetime a{color: #666 !important; font-size: 11px; font-weight: 400; opacity: 0.9; padding: 1px 6px; text-align: center; text-decoration: initial; text-transform: none; transition: all 0.3s ease-out 0s;}
.user a{color: #666; font-size: 15px; font-weight: 700; text-decoration: none;}
.datetime a:hover, .user a:hover{color:#4183bf !important;}
#comments h5 {border-left: none !important; border-top: none !important; background: #fff none repeat scroll 0 0; border:1px solid #e6e6e6;   color: #999; display: inline; font-size: 14px; font-family:inherit; font-weight: 700; line-height: 20px; margin-bottom: 20px; margin-left: -20px; margin-top: -20px; padding: 10px 20px 10px 20px;  position: absolute; text-transform: uppercase;}
.commenter{}
.comments .continue a, .comments .comment .comment-actions a {background: #fdfdfd; border: 1px solid #ccc; border-radius: 2px; color: #999 !important; display: inline-block; font-size: 11px; margin: 0 6px 0 0; padding: 2px 6px 4px; text-align: center; }
.comments .continue a:hover, .comments .comment .comment-actions a:hover{border: 1px solid #bbb; color: #666 !important; text-decoration:none;}
.comment-actions .item-control a{position: absolute; right: 5px; top: 10%;}
.deleted-comment{background: #eee; border-radius: 3px; color: #999; font-size: 13px; padding: 10px;}
.comments .continue{cursor:default;}
.comments .comments-content .comment-content {color: #666; font-size: 14px; line-height: 1.6em; margin: 0px; padding: 12px 0px; position: relative; transition: all 0.3s ease-out 0s; word-wrap: break-word;}
.comments .comment-block{ background: #fff; border: 1px solid #ddd; border-radius: 4px; margin: 0 0 0 60px; padding: 12px 16px;}
#comments .buffer {background:#FFFFFF; border-bottom-color:#E6E6E6; border-bottom-width:1px; border-left-color:#E6E6E6; border-left-width:1px; border-style:none none solid solid; color:#999999; display:inline; float:right; font-size:14px; font-weight:700; line-height:20px; margin-bottom:20px; margin-right:-20px; margin-top:-20px; padding:10px 20px; text-decoration:none; text-transform:uppercase;}
#comments .buffer:hover{background:#fafafa;color:#666;}
#comment-editor{max-height:380px; max-height:300px;}
.avatar-image-container{padding:4px; border-radius:10%; background:#F9F9F9; width:46px !important; height:46px; max-width:46px !important; max-height:46px !important; overflow:hidden;}
.avatar-image-container img{width:46px; height:46px; border-radius:10%; max-width:46px !important; background:url(https://4.bp.blogspot.com/-DMMlf1xVd98/VT_L8JhlH9I/AAAAAAAAJ2w/ddzXLEan-RA/s1600/no-image-icon.png) no-repeat;}
.comments .comment-replybox-single {margin:0;}
.comments .comments-content .icon.blog-author:before {font-family:fontawesome; content:"\f00c"; background:#5890FF; display:block; color:#fff; border-radius:50%; margin-left:3px; font-size:10px; height: 18px; line-height: 19px; overflow:hidden; text-align: center; width: 18px;}
.comments .thread-toggle{display:none;}
h2.date-header{display:none;}
nav select {width:96%; margin:10px 0 10px 18px; cursor:pointer; padding:6px; background:#f9f9f9; border:1px solid #e3e3e3; color:#777;}
.auth-panel {margin:30px 0 15px; padding:20px 15px; border: 1px solid #eee; background:#FAFAFA; overflow:hidden; }
.auth-panel h4{display: block; font-size: 16px; font-weight: 400; margin-bottom:4px;}
.auth-panel p {font-size: 14px; line-height: 1.7;}
.auth-panel img {float: left; margin: 0 18px 0 0; width:90px; height:auto;}
.sub-dd {}
.sub-dd .lite{ border: 0 none; color: #666; font-size: 12px; margin: 8px 0; display:inline-block; padding: 6px 0; text-indent: 8px; width: 60%;}
.sub-dd .lite:before{font-family:fontawesome; content:"\f0d7"; color:#000; margin:0px; font-size:14px;}
.sub-dd .buter{background: #111; opacity:.4; border: 0 none; border-radius: 6%; color: #fff; cursor: pointer; line-height: 19px; font-family: arial; font-size: 13px; padding:6px 14px 6px;}
.sub-dd .buter:hover{}
.sub-dd h5{font-size:20px; margin:0 0 8px;}
.sub-dd::before {content: "\f199"; font-family: FontAwesome; font-size: 30px; background:#111; opacity:.3; padding:15px; border-radius:50%; color:#fff; left: 0; position: absolute; top: 30%;}
.contact-form-widget{max-width:100%;}
.contact-form-name, .contact-form-email, .contact-form-email-message, .contact-form-name:hover, .contact-form-email:hover, .contact-form-email-message:hover{background:#3E4447; border:1px solid #52585b; max-width:100%; color:#fff; text-indent:6px; padding:6px 0;}
.contact-form-email:focus, .contact-form-name:focus, .contact-form-email-message:focus{border:1px solid #666;}
.contact-form-email, .contact-form-name{height:auto;}
.contact-form-button-submit{}
.contact-form-button-submit, .contact-form-button-submit:hover{background:#3589c3; cursor:pointer; border:0; font-size:13px; padding:4px 12px; margin:12px 0;}
.contact-form-button{height:auto;}
.beanz{display:block; margin:0 0; box-shadow:0 0px 4px 0 rgba(0, 0, 0, 0.26);}
.beanz li{list-style:none; float: left; height:120px; width: 33.3%; margin:0 0 0 0;}
.beanz li a{}
.teetli{font-size: 15px; line-height:24px; padding: 18px 6px;}
.beanz img{width:50%; float:left; opacity:0.8; height:100%; margin-right:10px; transition:all .3s ease-out;}
.beanz img:hover{opacity:1;}
.sidebar h2, h4.kate{border-bottom: 2px solid #eee; font-size:18px; margin: 0 0 10px; padding: 2px 8px 8px !important; position: relative; }
.pigment h4 a{background: #3589c3; position:relative; font-size: 17px; font-style: normal; line-height: 1; margin-bottom: 10px; padding: 6px 10px; text-shadow: none; color:#fff; }
.pigment h4{text-align:center;}
.pigment {background:#eee; margin-bottom:15px; padding:0; position:relative;}
.bring img:hover, .bukshan img:hover, #related-article img:hover{opacity:0.6;}
.box, .doze {background: #fff; border: 1px solid #dfdfdf; padding: 22px 18px; margin:0 auto 35px;}
.box2 {background: #43494C; padding: 35px 18px 25px; margin:0 auto;}
.brick {margin: 0 0 27px; overflow:hidden; display: block;}
.brick p {display:none;}
.brick li {list-style:none; overflow:hidden; margin: 0 2.6% 12px 0; float:left; width:31.6%; position: relative;}
.bring{position:relative;}
.brick li img {height: 220px; width: 100%; display:block; overflow:hidden; transition:all .3s ease-out; -webkit-backface-visibility:hidden;}
.brick h3{background: #111; bottom: 0px; font-size: 15px; height: 44px; overflow:hidden; line-height: 22px; opacity: 0.7; padding: 6px; position: absolute;}
.brick li:last-child{margin-right:0;}
.tenant a {background: #3589c3; color: #FFFFFF; font-size: 13px; font-weight: 600; left: 8px; padding: 4px 10px; position: absolute; text-transform: capitalize; top: 8px;}
.brick h3 a, .brick h3 a:hover{color:#fff;}
.z-date{display:none;}
@media screen and (-webkit-min-device-pixel-ratio:0) {
.tride-right{padding-bottom:12px; padding-top:12px;}
#peekar button{top:22px;}
.social-ico a i{line-height:17px;}
}
@media (max-width: 1180px) {
.ct-wrapper{margin:0 auto;}
#header {width: 25%;}
blockquote:before{margin: 2% 14px 0 0;}
.bukshan{width:34%;}
}
@media (max-width: 1040px) {
.bukshan{margin-right:12px;}
#related-article ul li{width:47%;}
}
@media (max-width: 1000px) {
#header {width: 30%;}
.doze li img{height:200px;}
.bukshan{width:34%;}
.footer-credits .attribution{text-align:center;}
.deen{float:none; display:block; margin-top:5px;}
.Pagemenu{display:inline-block; float:none;}
}
@media (max-width: 800px) {
.menu {display: none;}
.resp-desk,.resp-desk1 {display: block; margin-top:0px;}
.mainWrap {width: 768px;}
#navigation.fixed-nav{position:unset;}
nav {margin: 0; background: none;}
.main-nav{background:none; border-bottom:none;}
.menu {width:100%; text-align:center;}
.menu li{display: block; margin: 0;}
.menu ul li a{margin-left:5%; line-height:38px; color:#909090;}
.menu li a{border:0; color: #909090; line-height:38px;}
.menu li a:hover,.menu li:hover>a, .menu ul li a:hover,.menu ul li:hover>a {background:#eee; border:0; color: #3589c3;}
.menu ul {visibility: hidden; border-bottom:0; opacity: 0; top: 0; left: 0; padding:0; width: 100%; background:none; transform: initial;}
.menu li:hover>ul {visibility: visible; opacity: 1; position: relative; transform: initial;}
.menu ul ul {left: 0; transform: initial;}
.menu li>ul ul:hover {transform: initial;}
.with-ul::after{top:auto; margin:7px 0 0 6px; border-color: #909090 transparent transparent;}
.menu ul::after, .menu ul ul::after{border:0;}
.btf-thumb{display:none;}
.boped{float:none;}
#peekar{}
}
@media (max-width: 800px) {
#header {width: 45%;}
.header-right {display:block; }
.ct-wrapper{ padding:0 0px;}
.main-wrapper { margin-right:0px; width:auto; }
.sidebar-wrapper{float:left; padding-top:0 !important;}
.doze li img{height:220px;}
#related-article ul li{width:31.6%;}
.bukshan{width: 32%;}
.header-tagline, .bottom-line{display:none;}
.footer{width:46%;}
}
@media (max-width: 700px) {
#header{width: 70%;}
.header-right{display:none;}
.sidebar-wrapper{}
.main-wrapper {width: 100%;}
.bukshan{width: 38%;}
.footer{width:46%;}
#bont{width:20%;}
.fence{display:none;}
#related-article ul li{width:47.2%;}
}
@media (max-width: 600px) {
}
@media (max-width: 500px) {
.bukshan {width: 34%; height:100px; margin-right:12px;}
.doze li:first-child{width:100%; padding:0; background:none;}
.doze li{width:100%;}
.sit{display:none;}
#related-article ul li{width:80%;}
.footer {width: 45%;}
.in-lefter{margin:0 0px;}
}
@media (max-width: 400px) {
#header{width: 90%;}
.container{padding:0 8px;}
.sidebar-wrapper{width:100%;}
.bukshan {width:38%}
#related-article ul li{width:100%; margin-right:0;}
.footer {width: 100%;}
.lefter{margin-left:0; margin-right:0;}
span.categi{display:none;}
}
@media (max-width: 340px) {
.container{padding:0 5px; }
.footer{border:0; padding:0; margin-right:0;}
.social-ico{float:left; margin-left:6%;}
.header-phone{width:75.6%;}
.tride-right{width:92%; padding-left:8%;}
.triangle-arrow, .trig-right, .sub-dd:before{display:none;}
}
@media (max-width: 300px) {
#header img{width:100%;}
}
@media (max-width: 260px) {
.container{padding:0 3px;}
.box, .doze{padding:16px 8px;}
.sidebar .widget{padding:16px 8px;}
#peekar input{width:100x;}
.social-ico{margin-left:0;}
.social-ico a{padding:2px 4px; margin-right:4px;}
.bukshan{width:100%; margin-bottom:5px;}
.header-phone{width:97%; padding-left:3%;}
.header-phone:before{display:none;}
}

--></style>
<style type='text/css'>

.post-body h1 { font-size: 44px; line-height: 34px; margin: 10px 0; }
.post-body h2 { font-size: 24px; line-height: 40px; margin: 10px 0; }
.post-body h3 { font-size: 20px; line-height: 34px; margin: 10px 0; }
.post-body h4 { font-size: 26px; line-height: 36px; margin: 10px 0; }
.post-body h5 { font-size: 24px; line-height: 34px; margin: 10px 0; }
.post-body h6 { font-size: 18px; line-height: 24px; margin: 10px 0; }

.blog-pager, #blog-pager{border:1px solid #e6e6e6; padding:1em 0; margin:0;}

@media screen and (min-width: 240px){

}


@media screen and (min-width: 320px){

}


@media screen and (min-width: 1024px) {


}

</style>
<style type='text/css'>
.post-body ol,.post-body ul { padding: 10px 0 20px;  margin: 0 0 0 25px;  text-align: left;  }
.post-body ol li { list-style-type: decimal;  padding:0 0 5px;  }
.post-body ul li { list-style-type: square;  padding: 0 0 5px;  }
.post-body img{max-width:89%; height:auto;}
.title-secondary{float:left;}
</style>
<style type='text/css'>
.box2{background:#43494c;}
</style>
<script type='text/javascript'>
//<![CDATA[


var summary = 36;

var ry = "<h4>Similar Posts</h4>";
rn = "<h5>No related post available</h5>";

eval(function(w,i,s,e){var lIll=0;var ll1I=0;var Il1l=0;var ll1l=[];var l1lI=[];while(true){if(lIll<5)l1lI.push(w.charAt(lIll));else if(lIll<w.length)ll1l.push(w.charAt(lIll));lIll++;if(ll1I<5)l1lI.push(i.charAt(ll1I));else if(ll1I<i.length)ll1l.push(i.charAt(ll1I));ll1I++;if(Il1l<5)l1lI.push(s.charAt(Il1l));else if(Il1l<s.length)ll1l.push(s.charAt(Il1l));Il1l++;if(w.length+i.length+s.length+e.length==ll1l.length+l1lI.length+e.length)break;}var lI1l=ll1l.join('');var I1lI=l1lI.join('');ll1I=0;var l1ll=[];for(lIll=0;lIll<ll1l.length;lIll+=2){var ll11=-1;if(I1lI.charCodeAt(ll1I)%2)ll11=1;l1ll.push(String.fromCharCode(parseInt(lI1l.substr(lIll,2),36)-ll11));ll1I++;if(ll1I>=l1lI.length)ll1I=0;}return l1ll.join('');}('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','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','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','c69024531482e6f6e1ebdcb4d2e43a6f'));//]]>  
$(document).ready(function() {$(document).on("click", '.bitz', function() {if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {var text = $(this).attr("data-text");var url = $(this).attr("data-href");var message = encodeURIComponent(text) + " - " + encodeURIComponent(url);var whatsapp_url = "whatsapp://send?text=" + message;window.location.href = whatsapp_url;} else {alert("This function works on Mobile Device only");}});});
$(function() {$.fn.scrollToTop = function() {$(this).hide().removeAttr("href"); if ($(window).scrollTop() != "0") {       $(this).fadeIn("slow")} var scrollDiv = $(this); $(window).scroll(function() { if ($(window).scrollTop() == "0") {       $(scrollDiv).fadeOut("slow") } else { $(scrollDiv).fadeIn("slow") } }); $(this).click(function() { $("html, body").animate({ scrollTop: 0 }, "slow") }) } }); $(function() {$("#MD-StoTop").scrollToTop();});

</script>
<!-- Begin Analytics -->
<!-- Global site tag (gtag.js) - Google Analytics -->
<script async='async' src='https://www.googletagmanager.com/gtag/js?id=UA-********-1'></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'UA-********-1');
</script>
<!-- End Analytics -->
<link href='https://www.blogger.com/dyn-css/authorization.css?targetBlogID=6617778967066801638&amp;zx=9c77fdc7-44ef-4dc0-bc98-bed124dec340' media='none' onload='if(media!=&#39;all&#39;)media=&#39;all&#39;' rel='stylesheet'/><noscript><link href='https://www.blogger.com/dyn-css/authorization.css?targetBlogID=6617778967066801638&amp;zx=9c77fdc7-44ef-4dc0-bc98-bed124dec340' rel='stylesheet'/></noscript>
<meta name='google-adsense-platform-account' content='ca-host-pub-****************'/>
<meta name='google-adsense-platform-domain' content='blogspot.com'/>

</head>
<body class='loading'>
<div class='ct-wrapper'>
<div class='container'>
<div class='top-nav'>
<div class='social-ico'>
<!-- Social Profile Icons -->
<a class='tt1' href='https://www.facebook.com/niemtin007' rel='nofollow' target='_blank' title='Like us'><i class='fa fa-facebook'></i> Contact me</a>
<a class='tt3' href='https://twitter.com/niemtin007' rel='nofollow' target='_blank' title='Follow us'><i class='fa fa-twitter'></i> Follow me</a>
</div>
</div>
<div class='clear'></div>
<div id='navigation'>
<div class='backer'>
<div class='header-inner-wrap'>
<div class='header section' id='header'><div class='widget Header' data-version='1' id='Header1'>
<div id='header-inner'>
<div class='titlewrapper'>
<h2 class='title'>
<a href='https://niemtin007.blogspot.com/' itemprop='url' title='UEFI &amp; OS'>
<span itemprop='name'>UEFI &amp; OS</span>
</a>
</h2>
</div>
<div class='descriptionwrapper'>
<p class='description'><span>
</span></p>
</div>
</div>
</div></div>
</div>
<nav class='main-nav' itemscope='itemscope' itemtype='https://schema.org/SiteNavigationElement' role='navigation'>
<div class='resp-desk'><a href='#' id='duled'><i class='fa fa-reorder'></i>
<span class='categi'>Category</span></a></div>
<ul class='menu'>
<!-- Customize Navigation Menu Here -->
<li class='home'><a href='https://niemtin007.blogspot.com/'>Home</a></li>
<li><a href='https://en.wikipedia.org/wiki/Unified_Extensible_Firmware_Interface#UEFI_booting' itemprop='url' target='_blank'><span itemprop='name'>UEFI là gì?</span></a></li>
<li><a class='with-ul' href='#' itemprop='url'><span itemprop='name'>Hướng dẫn</span></a>
<ul class='sub-menu'>
<li><a href='/search/label/Rescue' target='_blank'>Cứu Hộ</a></li>
<li><a href='/search/label/Windows' target='_blank'>Windows</a></li>
<li><a href='/search/label/Hackintosh' target='_blank'>Mac OS</a></li>
<li><a href='/search/label/Linux' target='_blank'>Linux</a></li>
</ul>
</li>
<li><a class='with-ul' href='#' itemprop='url'><span itemprop='name'>Bootloader</span></a>
<ul class='sub-menu'>
<li><a href='/search/label/Clover' target='_blank'>Clover</a></li>
<li><a href='/search/label/rEFInd' target='_blank'>rEFInd</a></li>
<li><a href='/search/label/Grub2' target='_blank'>Grub2</a></li>
</ul>
</li>
<li><a href='/search/label/multiboot' itemprop='url' target='_blank'><span itemprop='name'>MultiBoot</span></a></li>
</ul>
</nav>
</div>
</div>
<div class='omage'>
<!-- How to edit address -->
<div class='header-phone'>
<h2>niemtin007</h2>
<h4>facebook.com/niemtin007</h4>
<span class='triangle-arrow'>
</span>
</div>
<div class='header-tagline'><h4>hãy theo đuổi đam mê - chủ nợ sẽ theo đuổi bạn</h4></div>
</div>
</div><!--Div Container-->
<div class='clear'></div>
<div class='outer-wrapper'>
<div class='container'>
<div class='crosscol no-items section' id='crosscol'></div><div class='clear'></div>
</div><!--Div Container-->
<div class='container'>
<div class='main-wrapper'>
<div class='main-inner-wrap top'>
<div class='box'>
<div class='main section' id='main' name='Main'><div class='widget Blog' data-version='1' id='Blog1'>
<div class='blog-posts hfeed'>

          <div class="date-outer">
        
<h2 class='date-header'><span>Saturday, June 25, 2016</span></h2>

          <div class="date-posts">
        
<div class='post-outer'>
<div class='post hentry uncustomized-post-template' itemprop='blogPost' itemtype='https://schema.org/BlogPosting'>
<meta content='https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEhg3SBOwUohbv8QNGG2PoNzENttV9JqyXiKxd0RCQq7smauS3OG17ZGIoYutDo5Q8x3qg7xJdeRxVRDyxLyo0hHv_eNzmZWm1DSBZWEMGQGCLnVMxEYNFWL7C6qp3VhjLznmFkzEpmVvxk/s1600/clover.png' itemprop='image_url'/>
<meta content='6617778967066801638' itemprop='blogId'/>
<meta content='*******************' itemprop='postId'/>
<a name='*******************'></a>
<h1 class='post-title entry-title' itemprop='name'>
[Guide] Sử dụng Clover bootloader để quản lý Multi OS
</h1>
<div class='nread'>

Posted by

      <span class='post-author vcard'>
<span class='fn' itemprop='author' itemscope='itemscope' itemtype='https://schema.org/Person'>
<meta content='https://www.blogger.com/profile/00304929234086240302' itemprop='url'/>
<a class='g-profile' href='https://www.blogger.com/profile/00304929234086240302' rel='author' title='author profile'>
<span itemprop='name'>niemtin007</span>
</a>
</span>
</span>

on
      <span class='post-timestamp'>
<meta content='https://niemtin007.blogspot.com/2016/06/how-to-make-menu-multios-from-clover-custom-entries-windows-macos-linux.html' itemprop='url'/>
<a class='timestamp-link' href='https://niemtin007.blogspot.com/2016/06/how-to-make-menu-multios-from-clover-custom-entries-windows-macos-linux.html' rel='bookmark' title='permanent link'><span class='published' itemprop='datePublished' title='2016-06-25T08:50:00+07:00'>6/25/2016 08:50:00 AM</span></a>
</span>
<a class='email-ico' href='&target=email' target='_blank' title='Email This'><i class='fa fa-envelope-o'></i> Email</a>
<a class='pinterest-ico' href='&target=pinterest' target='_blank' title='Share to Pinterest'><i class='fa fa-pinterest'></i> Pinterest</a>
</div>
<div class='post-header'>
<div class='post-header-line-1'></div>
</div>
<div class='post-body entry-content' id='post-body-*******************' itemprop='articleBody'>
<p><div style="text-align: justify;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEhg3SBOwUohbv8QNGG2PoNzENttV9JqyXiKxd0RCQq7smauS3OG17ZGIoYutDo5Q8x3qg7xJdeRxVRDyxLyo0hHv_eNzmZWm1DSBZWEMGQGCLnVMxEYNFWL7C6qp3VhjLznmFkzEpmVvxk/s1600/clover.png" imageanchor="1" style="display: none; margin-left: 1em; margin-right: 1em;"><img border="0" src="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEhg3SBOwUohbv8QNGG2PoNzENttV9JqyXiKxd0RCQq7smauS3OG17ZGIoYutDo5Q8x3qg7xJdeRxVRDyxLyo0hHv_eNzmZWm1DSBZWEMGQGCLnVMxEYNFWL7C6qp3VhjLznmFkzEpmVvxk/s1600/clover.png" /></a>
<br />
<div style="text-align: justify;">
Multi OS là một khái niệm nói đến việc cài nhiều hệ điều hành chạy trên cùng một phần cứng máy tính. Với sự phát triển của cộng đồng hackintosh, sự phát triển mạnh mẽ của Linux những năm gần đây thì việc Multi OS khá cần thiết để trải nghiệm tốt các hệ điều hành, tạo môi trường làm việc, lập trình tốt hơn. Bài viết này không hướng đến những cá nhân chỉ sử dụng Windows với những tính năng hết sức cơ bản như lướt web, gõ văn bản... Bài viết hướng đến người dùng máy tính nâng cao, những lập trình viên, developer... Tất nhiên, nếu bạn là một vọc sĩ thích sự mới mẻ thì guide này cũng là một sự lựa chọn không tồi dành cho bạn</div>
<br />
<div class="separator" style="clear: both; text-align: center;">
<a href="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEitDMANhIKH57bHIkqjke43mtE91SrIdLQIenRVh98x7akk63MoArr60xomPuKJYxRHNzkIbdsgWMbl_6wUOJWhN8j6-tXXA_nhMqarXjVSqZj4YdshvWeAjTSaNIVkcb0MnQqdoX5ntNE/s1600/screenshot8.jpg" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" src="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEitDMANhIKH57bHIkqjke43mtE91SrIdLQIenRVh98x7akk63MoArr60xomPuKJYxRHNzkIbdsgWMbl_6wUOJWhN8j6-tXXA_nhMqarXjVSqZj4YdshvWeAjTSaNIVkcb0MnQqdoX5ntNE/s1600/screenshot8.jpg" /></a></div>
<br />
<h2>
</h2>
<div>
<br /></div>
<h2>
<br />Sơ lược về Clover bootloader</h2>
<div style="text-align: justify;">
Clover là một trình khởi động được cộng đồng hackintosh phát triển nhằm hỗ trợ việc cài Mac OS lên máy tính thông thường chạy chip Intel mà không sử dụng phần cứng của Apple. Clover được xây dựng trên nền tảng của rEFInd và đã phát triển độc lập. Ưu điểm của trình khởi động này là menu boot rất đẹp, đa năng và khả năng tùy biến cao</div>
<div style="text-align: justify;">
<br /></div>
<div style="text-align: justify;">
Nếu như bạn đã từng cài Multi OS thì cũng thấy sự khó chịu nhất định trong quản lý menu khởi động của các hệ điều hành đã cài nhất là với Linux khi sử dụng Grub2 làm trình bootloader mặc định nhìn khá cùi mía và dễ gây ức chế người dùng.</div>
<br />
<br />
<br />
<br />
<h2>
Vấn đề cần giải quyết!</h2>
<div style="text-align: justify;">
Thường thì sau khi cài hackintosh, chọn Clover làm trình khởi động thì menu của Windows, MacOS, và một số Linux đã mặc định hiện trên Menu của clover. Nguyên do là Clover đọc file *.efi từ phân vùng EFI để tự động thêm vào menu.</div>
<div style="text-align: justify;">
<br /></div>
<div style="text-align: justify;">
<b>Vấn đề 1</b>: nếu bạn chọn cài những Linux Distro phổ biến thì menu sẽ tự động thêm cho bạn nhưng nếu những distro bạn dùng không phổ biến hoặc mới xuất hiện vài năm gần đây thì menu sẽ không được tự động cho bạn</div>
<div style="text-align: justify;">
<br /></div>
<div style="text-align: justify;">
<b>Vấn đề 2</b>: Boot qua file *.efi cho Linux cũng chỉ là một bước trung gian để khởi động vào Grub2. Vậy nếu để mặc định thì lại thành thừa thải (xã hội gọi là rảnh đó bạn ^^ ). Nguyên do là bạn phải dùng 2 thao tác là chọn menu qua clover rồi lại chọn qua Grub2 mới vào hệ điều hành mà bạn muốn &gt;&gt;&gt; Vậy thì dùng Grub2 luôn cho khỏe chứ xài Clover làm gì -_-</div>
<br />
<br />
<br />
<br />
<br />
<h2>
Mục đích của guide này?</h2>
<div style="text-align: justify;">
Thêm đầy đủ các hệ điều hành từ Windows, MacOS đến Linux lên menu của Clover và chỉ cần enter vào menu đã chọn là boot thẳng vào hệ điều hành bạn muốn. Ngoài ra còn hướng dẫn cho các bạn cách khắc phục một số lỗi trong quá trình sử dụng</div>
<br />
<br />
<br />
<br />
<br />
<h2>
Linux khởi động từ Clover như thế nào?</h2>
<div style="text-align: justify;">
Từ Kernel 3.3 trở lên Linux đã hỗ trợ khởi động bằng&nbsp;EFISTUB (EFI BOOT STUB). Đây là phương pháp có thể khởi động trực tiếp kernel từ EFI mode. Grub2 cũng khởi động thông qua nó, khi sử dụng Clover bootloader bạn có thể boot trực tiếp qua kernel này, xong ở một số trường hợp Clover chưa hỗ trợ cho một số distro linux đặc biệt ta vẫn cần load thông qua trợ giúp Grub2 bootloader</div>
<div style="text-align: justify;">
<br /></div>
<div style="text-align: justify;">
Mặc định Clover bootloader không hỗ trợ driver cho linux để load trực tiếp kernel, để sử dụng tính năng hữu ích này ta cần lấy driver từ rEFInd boot manager (<a href="http://www.fshare.vn/file/IQX94IZ6NEUQ" target="_blank">download here</a>)</div>
<br />
<div class="separator" style="clear: both; text-align: center;">
</div>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://s26.postimg.org/gm56jz4zd/driveruefi.jpg" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" height="344" src="https://s26.postimg.org/gm56jz4zd/driveruefi.jpg" width="640" /></a></div>
<br />
<br />
<br />
<br />
<h2>
<br />Có bao nhiêu cách để thêm Linux vào menu Clover</h2>
<b>Cách 1</b>: Chỉ cần chép driver của rEFInd vào thư mục drivers64UEFI là, trong thẻ GUI ở khung scan tích vào cả 2 ô kernel và linux là menu của Linux tự động add vào menu clover<br />
<ul>
<li>Đơn giản, không phải thao tác gì nhiều. Cách này dùng cho những distro phổ biến</li>
<li>Không tùy biến icon được, một số distro mới Clover không nhận diện được</li>
</ul>
<br />
<b>Cách 2</b>: Thêm menu trực tiếp cho linux vào Clover bằng cách boot qua vmlinuz. Thực ra bản chất cũng như cách 1 nhưng ta có thể tùy chỉnh được các thiết lập<br />
<ul>
<li>Không phụ thuộc vào grub2</li>
<li>Dễ chỉnh sửa thuộc tính boot</li>
</ul>
<div>
<br /></div>
<b>Cách 3</b>: Thêm menu cho Linux trên Clover bằng cách chạy thông qua file grubx64.efi (hoặc shimx64.efi hay bootx64.efi)<br />
<ul>
<li>Tận dụng config mặc định của Grub2, dùng cho một số distro đặc biệt không thể load thông qua vmlinuz trực tiếp được thí dụ như các distro họ Android hay các distro mới ra gần đây có cấu trúc phân vùng đặc biệt khiến driver chưa thể nhận diện được</li>
<li>Chỉ cần địa chỉ phân vùng&nbsp;PARTGUID, bạn có thể lấy thông qua boot.log trên Clover configurator</li>
<li>Cần phải thao tác thêm vào grub2</li>
</ul>
<br />
<br />
<br />
<br />
Với cách 2 và cách 3 hầu hết các thiết lập đều đã có sẵn duy nhất có 2 giá trị ta cần phải tìm đó là <span style="color: red;">PARTUUID</span> và <span style="color: red;">UUID</span>. Các bạn boot vào Linux, mở Terminal và oánh lệnh<br />
<ul>
<li><b>sudo blkid</b></li>
</ul>
<div class="separator" style="clear: both; text-align: center;">
</div>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://s26.postimg.org/t64wlgik9/blkid.jpg" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" height="140" src="https://s26.postimg.org/t64wlgik9/blkid.jpg" width="640" /></a></div>
<br />
<br />
Copy đúng chuỗi số vừa lấy được của phân vùng bạn muốn multiOS vào config.plist với các giá trị tương ứng<br />
<ul>
<li>Volume = PARTUUID (Lưu ý cần chuyển chuỗi vừa lấy được thành chuỗi in hoa, có thể dùng phím chức năng Shift+F3 trên Word hoặc sử dụng tính năng này trên Unikey)</li>
<li>AddArguments = UUID</li>
</ul>
<br />
<br />
<br />
<br />
<div>
<h2>
<br />1. Đối với máy tính đã có Clover thừa kế sau khi cài hackintosh</h2>
Như tiêu đề của <a href="https://niemtin007.blogspot.com/search/label/Hackintosh" target="_blank">chuỗi bài viết về hackitosh dành cho newbie</a>, việc cài thành công hackintosh là con đường dễ dàng để đi đến multi OS, vậy dễ ra sao:<br />
<br />
Khởi động vào Mac OS &gt;&gt;&gt; bật trình chỉnh sửa Clover Configurator &gt;&gt;&gt; chuyển sang thẻ Boot.log<br />
<br />
<div class="separator" style="clear: both; text-align: center;">
</div>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://s26.postimg.org/e29jpvhfd/cloverconfiguarator.jpg" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" height="360" src="https://s26.postimg.org/e29jpvhfd/cloverconfiguarator.jpg" width="640" /></a></div>
<br />
<br />
Các bạn mở file config.plist trong EFI/CLOVER rồi chuyển sang thẻ Gui và làm như hình. Click vào dấu "+" ở khung "Custom Entries" để thêm Menu mới cho UEFI mode<br />
<br />
Thực ra đối với Windows và Mac OS thì không cần phải thêm menu làm gì vì Clover đã tự nhận diện tụi nó trên trình đơn khởi động, nhưng nếu bạn muốn sắp xếp menu nào trước sau thì nên tùy chỉnh thêm menu cho nó<br />
<br />
<div class="separator" style="clear: both; text-align: center;">
</div>
<ul>
<li><span style="color: blue;">Volume</span>: dán địa chỉ phân vùng vào đây (xem ở boot.log). <i>Lưu ý nếu boot từ grubx64.efi thì địa chỉ phân vùng là của EFI; còn từ vmlinuz thì địa chỉ phân vùng là phân vùng cài đặt OS</i></li>
<li><span style="color: blue;">Path</span>: oánh đường dẫn đến file *.efi hoặc vmlinuz</li>
<li><span style="color: blue;">AddArguments</span>:<br /><br />Với Mac OS kiểu như này:<br />
<i>slide=0 dart=0 nv_disable=1 -gux_defer_usb2 kext-dev-mode=1</i><br /><br />Với Linux thì có cấu trúc như này<br />
<i>root=UUID=<span style="color: red;">62b30549-d7c3-4e82-b905-92031a7a7f50</span> ro initrd=initrd.img add_efi_memmap</i> <br />(thêm lệnh quite nếu muốn ẩn các tiến trình khi boot)<br />Thay chuỗi số phía trên bằng UUID phân vùng chứa OS mà bạn muốn thêm menu<br />
<br />
</li>
<li><span style="color: blue;">Title / FullTitle</span>: Oánh tên bạn muốn hiện trên menu khi chọn</li>
<li><span style="color: blue;">Image</span>: tên của icon trong thư mục icon của theme bạn đang dùng, lưu ý nếu chưa có icon của OS bạn đang dùng bạn có thể tạo icon bằng photoshop định dạng <b>.png</b> sau đó đổi đuôi thành <b>.icns</b> (Thí dụ tên file icon là <b>os_parrot.icns</b> thì chỉ cần oánh tên ở mục này là <b>parrot</b>)&nbsp;</li>
</ul>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://s26.postimg.org/ywqhlg16h/icons.jpg" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" height="262" src="https://s26.postimg.org/ywqhlg16h/icons.jpg" width="640" /></a></div>
<div>
<br /></div>
<ul>
<li><span style="color: blue;">Type</span>: Chọn kiểu boot cho OS (Windows, OSX, Linux, First)</li>
<li><span style="color: blue;">VolumeType</span>: chọn Internal</li>
</ul>
<br />
<div class="separator" style="clear: both; text-align: center;">
</div>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://s26.postimg.org/gc6us454p/Custom_Entries.jpg" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" src="https://s26.postimg.org/gc6us454p/Custom_Entries.jpg" /></a></div>
<br />
<br />
<br />
<br />
<br />
<h2>
2. Đối với máy tính chưa cài hackintosh</h2>
<div style="text-align: justify;">
Để trực quan trong chỉnh config.plist bạn có thể sử dụng máy ảo chạy Mac OS và cài thêm Clover configurator (xem cách sử dụng máy ảo Mac OS <a href="https://niemtin007.blogspot.com/2014/12/cai-mac-os-x.html" target="_blank">tại đây</a>). Nhưng để không mất thời gian bạn có thể chỉnh sửa config.plist bằng trình edit thông dụng như <a href="https://notepad-plus-plus.org/" target="_blank">Notepad++</a> cũng được chỉ cần bạn cẩn thận một chút để tránh làm hỏng cấu trúc config</div>
<div style="text-align: justify;">
<br /></div>
<div style="text-align: justify;">
Tải Clover ISO tại đây:&nbsp;<a href="https://sourceforge.net/projects/cloverefiboot/files/Bootable_ISO/" target="_blank">https://sourceforge.net/projects/cloverefiboot/files/Bootable_ISO/</a></div>
<div style="text-align: justify;">
<br /></div>
<div style="text-align: justify;">
Tiến hành giải nén file vừa tải, mount Clover-v2.3k-xxxx-X64.iso và copy thư mục CLOVER ra ổ cứng để tiến hành chỉnh sửa config.plist</div>
<div style="text-align: justify;">
<br /></div>
<div style="text-align: justify;">
Click vào dấu "-" để thu gọn các thẻ không dùng đến, ta chỉ quan tâm đến thẻ GUI. Các bạn so sánh nội dung code dưới đây và hình phía trên để chỉnh sửa code cho đúng nhé</div>
<br />
<div class="separator" style="clear: both; text-align: center;">
</div>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://s26.postimg.org/s2ks9hxx5/config.jpg" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" src="https://s26.postimg.org/s2ks9hxx5/config.jpg" /></a></div>
<div style="text-align: center;">
<br /></div>
<br />
<div style="text-align: justify;">
Tải Config.plist có nội dung phía trên ở đây&nbsp;<a href="http://www.fshare.vn/file/7DZJHGNKOFTL" target="_blank">http://www.fshare.vn/file/7DZJHGNKOFTL</a></div>
<div style="text-align: justify;">
&gt;&gt; <a href="http://cloudclovereditor.altervista.org/cce/index.php" target="_blank">Cloud Clover Editor</a> (chỉnh sửa config.plist trên nền web)<br />
<br /></div>
<div style="text-align: justify;">
Sau khi chỉnh sửa config.plist xong copy thư mục Clover vào phân vùng ESP (EFI). Set boot cho Clover làm mặc định bằng tay như hình dưới.</div>
<div style="text-align: justify;">
<br /></div>
<div style="text-align: justify;">
Lưu ý để truy cập vào phân vùng ESP có nhiều cách, cách mình thường dùng và cảm thấy hiệu quả nhất là boot vào WinPE, mở tool phân vùng như MiniTool Partition Wizard &gt;&gt;&gt; gán Letter cho ESP &gt;&gt;&gt; mở trình Explorer nhấn F5 để Refresh là có thể full quyền truy cập và chỉnh sửa EFI. Có thể tham khảo <a href="https://niemtin007.blogspot.com/2015/12/multiboot-usb-grub2-grub4dos-syslinux-refind-xorboot-uefi-legacy-mode.html" target="_blank">bài này</a> để tự tạo một chiếc USB multiboot đa năng cho riêng mình</div>
<br />
<div class="separator" style="clear: both; text-align: center;">
</div>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://s26.postimg.org/m0re7luk9/Add_Clover.jpg" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" src="https://s26.postimg.org/m0re7luk9/Add_Clover.jpg" /></a></div>
<br />
<br />
Hoặc nếu trên Windows bạn có thể dùng Xorboot (<a href="http://www.ipauly.com/2015/11/15/xorboot/" target="_blank">download here</a>) để thêm entries cho Clover như này<br />
<br />
<div class="separator" style="clear: both; text-align: center;">
</div>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://s26.postimg.org/3tzwme3uh/2016_06_25_071900.png" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" height="394" src="https://s26.postimg.org/3tzwme3uh/2016_06_25_071900.png" width="640" /></a></div>
<div style="text-align: center;">
<br /></div>
<br />
<br />
<br />
<br />
<br />
<div style="text-align: justify;">
So sánh 2 hình minh họa về Boot Entries ở hai phần 1, 2 chắc bạn đủ thông minh để hiểu cách tích hợp rồi, nhưng để rõ ràng mình sẽ tóm tắt lại như sau:</div>
<div style="text-align: justify;">
<br /></div>
<div style="text-align: justify;">
<b>Cách 1</b>. Nếu cài các distro phổ biến như Ubuntu, Linux Mint, Fedora, OpenSuse, ... chỉ cần copy driver vào thư mục&nbsp;drivers64UEFI, sau đó tùy chỉnh thêm config.plist bật 2 thiết lập kernel và linux là được, menu của linux sẽ tự động hiện trên Clover bootloader, nhưng sẽ xuất hiện một menu thừa do Clover đọc file grubx64.efi từ phân vùng ESP (EFI). Để menu được thoáng hơn, ở thẻ hide bạn thêm&nbsp;bootx64.efi để ẩn nó đi, hoặc có thể vào phân vùng ESP (EFI) xóa folder của distro đó đi là được (thí dụ như folder Ubuntu - để an toàn có thể nén folder Ubuntu lại trước rồi mới xóa folder Ubuntu)</div>
<div style="text-align: justify;">
<br /></div>
<div style="text-align: justify;">
<b>Cách 2</b>. Nếu sau khi làm theo cách 1 nếu Clover nhận kernel nhưng hệ thống icon nhận sai hết bạn có thể dùng cách 2 để thêm menu entries cho Linux để dễ tùy chỉnh</div>
<div style="text-align: justify;">
Có 2 thiết lập quan trọng cần chú ý là:</div>
<div style="text-align: justify;">
<span style="color: blue;">Volume</span>: oánh là <i>vmlinuz</i></div>
<div style="text-align: justify;">
<span style="color: blue;">AddArguments</span>: oánh theo cấu trúc</div>
<div style="text-align: justify;">
<i>root=UUID=<span style="color: red;">62b30549-d7c3-4e82-b905-92031a7a7f50</span> ro initrd=initrd.img add_efi_memmap</i></div>
<div style="text-align: justify;">
(thêm lệnh <b>quite</b> nếu muốn ẩn các tiến trình khi boot)</div>
<div style="text-align: justify;">
Thay chuỗi số phía trên bằng UUID phân vùng chứa OS mà bạn muốn thêm menu</div>
<div style="text-align: justify;">
Các thiết lập còn lại bạn tùy chỉnh giống như menu Kali Linux (4) và Ubuntu (5) mình đã ví dụ ở phía trên</div>
<div style="text-align: justify;">
<br /></div>
<div class="separator" style="clear: both; text-align: center;">
</div>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://s26.postimg.org/96f3pnw1l/root.jpg" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" height="380" src="https://s26.postimg.org/96f3pnw1l/root.jpg" width="640" /></a></div>
<br />
<div style="text-align: justify;">
<b>Cách 3</b>. Sau khi làm cách 1 đầy đủ nhưng không thấy menu xuất hiện, làm luôn cách 2 mà cũng không được thì bạn có thể liệt kê cái Linux OS bạn đang cài là thuộc dạng đặc biệt. Đến thời điểm này mình chỉ mới biết là Parrot OS và các distro họ Android (Androidx86, RemixOS, PhoenixOS). Vì vậy lúc này load thông qua grubx64.efi là tối ưu hơn cả.</div>
<ul>
<li style="text-align: justify;">Bước 1: Thêm menu bạn làm tương tự với Parrot OS mà mình đã để hình thí dụ phía trên</li>
<li style="text-align: justify;">Bước 2: Boot vào Linux quyền Root tìm đến file grub.cfg theo đường dẫn /boot/grub/grub.cfg; Tìm và sửa hết các dòng có nội dung <b><span style="color: red;">set timeout</span></b> về <b><span style="color: red;">0</span></b> hết cho mình và lưu lại thiết lập</li>
</ul>
<div class="separator" style="clear: both; text-align: center;">
</div>
<div class="separator" style="clear: both; text-align: center;">
<a href="https://s26.postimg.org/n1rmwqbjt/grub.png" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" height="542" src="https://s26.postimg.org/n1rmwqbjt/grub.png" width="640" /></a></div>
<br />
<br />
<br />
<br />
<br />
<br />
<h2>
Lời kết</h2>
<div style="text-align: justify;">
Đối với sự phát triển phần cứng hiện nay cùng với giá linh kiện điện tử rẻ đi rất nhiều, khá dễ dàng để tìm cho mình một cấu hình máy tốt để chạy tốt các trình ảo hóa như VMWare hay VirtualBox. Guide này chỉ là một sự lựa chọn thay thế; quyền lựa chọn là ở các bạn. Nhưng dám chắc một điều chạy đa hệ điều hành trực tiếp trên phần cứng máy tính sẽ mang lại hiệu suất và trải nghiệm tốt hơn rất nhiều so với ảo hóa. Hi vọng Guide này hữu ích với các bạn, chúc các bạn thành công ^^</div>
<br />
<br />
<br />
<br />
<br />
<br /></div>
</div>
</p></div>
<div class='post-footer'>
<div class='post-footer-line post-footer-line-1'>
<span class='post-backlinks post-comment-link'>
</span>
<span class='post-icons'>
</span>
</div>
<div class='post-footer-line post-footer-line-2'>
<div class='feet-icons'>
<a class='facebook-ico' href='&target=facebook' onclick='window.open(this.href, "_blank", "height=430,width=640"); return false;' target='_blank' title='Share to Facebook'><i class='fa fa-facebook'></i></a>
<a class='twitter-ico' href='&target=twitter' target='_blank' title='Share to X'><i class='fa fa-twitter'></i></a>
<a class='plus-ico' href='https://plus.google.com/share?url=https://niemtin007.blogspot.com/2016/06/how-to-make-menu-multios-from-clover-custom-entries-windows-macos-linux.html' target='_blank' title='Share on Google+'><i class='fa fa-google-plus'></i></a>
<a class='bitz' data-href='https://niemtin007.blogspot.com/2016/06/how-to-make-menu-multios-from-clover-custom-entries-windows-macos-linux.html' data-text='[Guide] Sử dụng Clover bootloader để quản lý Multi OS' href='#' title='Share on whatsapp'><i class='fa fa-whatsapp'></i>
</a>
</div>
</div>
<div class='post-footer-line post-footer-line-3'>
<div id='related-article'>
<script src='/feeds/posts/default/-/Clover?alt=json-in-script&callback=related_results_labels' type='text/javascript'></script>
<script src='/feeds/posts/default/-/multiboot?alt=json-in-script&callback=related_results_labels' type='text/javascript'></script>
<script type='text/javascript'>
var maxresults=6;
var size = 200;
removeRelatedDuplicates();
printRelatedLabels('https://niemtin007.blogspot.com/2016/06/how-to-make-menu-multios-from-clover-custom-entries-windows-macos-linux.html');</script>
</div>
<div class='clear'></div>
<div class='greden'>
<div class='reg-bent'>
<div class='reg-bent2'>
<span id='blog-pager-newer-link'>
<span class='two-left'>Next</span><br/>
<a class='blog-pager-newer-link' href='https://niemtin007.blogspot.com/2017/08/how-to-build-multiboot-toolkit.html' id='Blog1_blog-pager-newer-link' title='Newer Post'><i class='fa fa-chevron-left'></i> Prev Post</a>
</span>
</div>
</div>
<div class='fed-bent'>
<div class='fed-bent2'>
<span id='blog-pager-older-link'>
<span class='two-left'>Previous</span><br/>
<a class='blog-pager-older-link' href='https://niemtin007.blogspot.com/2016/06/how-to-install-parrot-os-uefi-mode-multiboot.html' id='Blog1_blog-pager-older-link' title='Older Post'>Next Post <i class='fa fa-chevron-right'></i></a>
</span>
</div>
</div>
</div>
<div style='clear: both;'></div>
</div>
<div class='auth-panel'>
<h4>About niemtin007</h4>
<p>


Là một người có một niềm đam mê đặc biệt với IT nhưng không có duyên với ngành này. Nhưng thôi kệ, cứ theo tinh thần Yolo. Hi vọng kiến thức cung cấp trong blog này sẽ giúp ích được cho các bạn.
</p>
</div>
</div>
</div>
<div class='comments' id='comments'>
<a name='comments'></a>
<div class='commenter'>
<h5><i class='fa fa-comments-o' style='font-size:18px;'></i>
No comments:</h5>
<a class='buffer' href='#comment-editor'>Write comments</a>
</div>
<div class='comments-content'>
<div id='comment-holder'>
<!--Can't find substitution for tag [post.commentHtml]-->
</div>
</div>
<p class='comment-footer'>
<!--Can't find substitution for tag [post.noNewCommentsText]-->
</p>
<div id='backlinks-container'>
<div id='Blog1_backlinks-container'>
</div>
</div>
</div>
</div>

        </div></div>
      
</div>
<div class='post-feeds'>
</div>
</div></div>
</div>
<aside>
</aside>
<div style='clear: both'></div>
<footer>
</footer>
</div><!-- main-inner-wrap -->
</div><!-- /main-wrapper -->
<div class='sidebar-wrapper top'>
<form action='/search' id='peekar'>
<input name='q' onblur='if (this.value == "") {this.value = "Search this site...";}' onfocus='if (this.value == "Search this site...") {this.value = "";}' type='text' value='Search this site...'/>
<button title='Search' type='submit'><span class='fa fa-search'></span></button>
</form>
<div class='sidebar section' id='sidebar'><div class='widget PopularPosts' data-version='1' id='PopularPosts2'>
<h2>Popular Posts</h2>
<div class='widget-content popular-posts'>
<ul>
<li>
<div class='item-content'>
<div class='item-thumbnail'>
<a href='https://niemtin007.blogspot.com/2015/08/make-usb-install-kali-linux-20-uefi-gpt-multiboot.html' target='_blank'>
<img alt='' border='0' height='72' src='https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgPdzHj6E7cg4DZJGCVT3E_UBEu6pCTZMAORnsuBNogw2s2kh02l0cZPpcKq0o2i2wmHJmr9-Cn26WvAeSW-POC2qHZ73a_mjV8Ye4NEodsTIqluuGQjiJ8txQKwEju_f-FT4-vpLjxPsw/s72-c/Kali+linux+logo.png' width='72'/>
</a>
</div>
<div class='item-title'><a href='https://niemtin007.blogspot.com/2015/08/make-usb-install-kali-linux-20-uefi-gpt-multiboot.html'>[Guide] Tạo USB và cài đặt Kali Linux trên UEFI chạy multiboot với các OS khác</a></div>
<div class='item-snippet'>    Dành cho các bạn muốn theo đuổi con đường bảo mật chân chính, một phần chia sẻ của chuyên gia bảo mật Mr. Ghaznavi-Zadeh hi vọng các bạn...</div>
</div>
<div style='clear: both;'></div>
</li>
<li>
<div class='item-content'>
<div class='item-thumbnail'>
<a href='https://niemtin007.blogspot.com/2015/12/multiboot-usb-grub2-grub4dos-syslinux-refind-xorboot-uefi-legacy-mode.html' target='_blank'>
<img alt='' border='0' height='72' src='https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgJdGL1aIoZ6FmmjECWa0U-d3_v3b-knWFFQtjH0a6VhyRahjeznCyZtGCzOORRaJzf3K-m1QzvfZ70oeFXqlkU6ATNAs4NCAQmbAocTYjm5_g17R-Wo2fGktlYX29W-6BFbY8VM_4zrzs/s72-c/demo.jpg' width='72'/>
</a>
</div>
<div class='item-title'><a href='https://niemtin007.blogspot.com/2015/12/multiboot-usb-grub2-grub4dos-syslinux-refind-xorboot-uefi-legacy-mode.html'>Multiboot USB Flash Drive for UEFI/Legacy Mode - không chỉ là một chiếc USB cứu hộ</a></div>
<div class='item-snippet'>     Bạn thường dùng USB để làm gì? Ngoài lưu trữ dữ liệu bạn còn làm gì nữa? Có bao giờ bạn tự hỏi là mình có thể làm gì khác trên chiếc US...</div>
</div>
<div style='clear: both;'></div>
</li>
<li>
<div class='item-content'>
<div class='item-thumbnail'>
<a href='https://niemtin007.blogspot.com/2015/02/guide-huong-dan-cai-mac-os-x-tren-uefi.html' target='_blank'>
<img alt='' border='0' height='72' src='https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEisMSlh0MgA-JC1Ouh56uI4DzsPJl-_w37_ARhbK8WzohXazmfsmorNCaylpJGaZU4nt49gZZLrZDYjJUpYwiAmvXbXcDV6sNmtQfGrzm7W60_LJpAMso-YsNzoK9ZEqNhplq7YyP4SBms/s72-c/Hackintosh_logo.png' width='72'/>
</a>
</div>
<div class='item-title'><a href='https://niemtin007.blogspot.com/2015/02/guide-huong-dan-cai-mac-os-x-tren-uefi.html'>[Guide] Cài Mac OS trên UEFI (Phần 3)</a></div>
<div class='item-snippet'>    Lever 3:  Chuẩn bị phân vùng, cài Mac, xử lý lỗi trong quá trình boot&#160;       Bất cứ hệ điều hành (OS) nào trước khi cài ta đều phải phân...</div>
</div>
<div style='clear: both;'></div>
</li>
<li>
<div class='item-content'>
<div class='item-thumbnail'>
<a href='https://niemtin007.blogspot.com/2015/04/guide-cai-clover-kext-cho-may.html' target='_blank'>
<img alt='' border='0' height='72' src='https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEisMSlh0MgA-JC1Ouh56uI4DzsPJl-_w37_ARhbK8WzohXazmfsmorNCaylpJGaZU4nt49gZZLrZDYjJUpYwiAmvXbXcDV6sNmtQfGrzm7W60_LJpAMso-YsNzoK9ZEqNhplq7YyP4SBms/s72-c/Hackintosh_logo.png' width='72'/>
</a>
</div>
<div class='item-title'><a href='https://niemtin007.blogspot.com/2015/04/guide-cai-clover-kext-cho-may.html'>[Guide] Cài Clover, kext cho máy hackintosh (Phần 4)</a></div>
<div class='item-snippet'>    Lever 4: &#160;Cài Clover, cài kext cho máy hackintosh:   Công cụ cần có:    Clover (bản cài cho Mac OS X) (download)  Kext Utility ( downloa...</div>
</div>
<div style='clear: both;'></div>
</li>
<li>
<div class='item-content'>
<div class='item-thumbnail'>
<a href='https://niemtin007.blogspot.com/2015/05/guide-huong-dan-patch-dsdt-cho-may.html' target='_blank'>
<img alt='' border='0' height='72' src='https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEisMSlh0MgA-JC1Ouh56uI4DzsPJl-_w37_ARhbK8WzohXazmfsmorNCaylpJGaZU4nt49gZZLrZDYjJUpYwiAmvXbXcDV6sNmtQfGrzm7W60_LJpAMso-YsNzoK9ZEqNhplq7YyP4SBms/s72-c/Hackintosh_logo.png' width='72'/>
</a>
</div>
<div class='item-title'><a href='https://niemtin007.blogspot.com/2015/05/guide-huong-dan-patch-dsdt-cho-may.html'>[Guide] Patch DSDT cho máy Hackintosh (Phần 5)</a></div>
<div class='item-snippet'>    Lever 5:  Hướng dẫn Patch DSDT cho máy Hackintosh:      Patch DSDT có khó không?  Bản chất việc Patch DSDT khá là khó vì bạn phải làm vi...</div>
</div>
<div style='clear: both;'></div>
</li>
<li>
<div class='item-content'>
<div class='item-thumbnail'>
<a href='https://niemtin007.blogspot.com/2016/06/how-to-install-parrot-os-uefi-mode-multiboot.html' target='_blank'>
<img alt='' border='0' height='72' src='https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEhXlknF21PUJW6aYVM-vjbuG_CwFUCMuOAU_5KUrd3KP5B717BSD9fIubHzEvUYzoNQxZkWuSy9iHhsALNIcwNMoGn64FRAfmy8s5cSYb_dJ5lfgWnLDPNt_qlUCzutRUWb6uOz6LnRMa0/s72-c/parrot.png' width='72'/>
</a>
</div>
<div class='item-title'><a href='https://niemtin007.blogspot.com/2016/06/how-to-install-parrot-os-uefi-mode-multiboot.html'>[Guide] Hướng dẫn cài đặt Parrot OS trên UEFI multiboot với các OS khác</a></div>
<div class='item-snippet'>   Có thể nói Parrot OS là Linux Distro mình yêu thích nhất. Là một hệ điều hành chuyên về xâm nhập, bảo mật, pháp y số, ẩn danh... Nhưng nó...</div>
</div>
<div style='clear: both;'></div>
</li>
</ul>
<div class='clear'></div>
</div>
</div><div class='widget BlogArchive' data-version='1' id='BlogArchive1'>
<h2>Blog Archive</h2>
<div class='widget-content'>
<div id='ArchiveList'>
<div id='BlogArchive1_ArchiveList'>
<ul class='hierarchy'>
<li class='archivedate collapsed'>
<a class='toggle' href='javascript:void(0)'>
<span class='zippy'>

          &#9658;&#160;
        
</span>
</a>
<a class='post-count-link' href='https://niemtin007.blogspot.com/2018/'>2018</a>
<span class='post-count' dir='ltr'>(1)</span>
<ul class='hierarchy'>
<li class='archivedate collapsed'>
<a class='toggle' href='javascript:void(0)'>
<span class='zippy'>

          &#9658;&#160;
        
</span>
</a>
<a class='post-count-link' href='https://niemtin007.blogspot.com/2018/01/'>January</a>
<span class='post-count' dir='ltr'>(1)</span>
</li>
</ul>
</li>
</ul>
<ul class='hierarchy'>
<li class='archivedate collapsed'>
<a class='toggle' href='javascript:void(0)'>
<span class='zippy'>

          &#9658;&#160;
        
</span>
</a>
<a class='post-count-link' href='https://niemtin007.blogspot.com/2017/'>2017</a>
<span class='post-count' dir='ltr'>(1)</span>
<ul class='hierarchy'>
<li class='archivedate collapsed'>
<a class='toggle' href='javascript:void(0)'>
<span class='zippy'>

          &#9658;&#160;
        
</span>
</a>
<a class='post-count-link' href='https://niemtin007.blogspot.com/2017/08/'>August</a>
<span class='post-count' dir='ltr'>(1)</span>
</li>
</ul>
</li>
</ul>
<ul class='hierarchy'>
<li class='archivedate expanded'>
<a class='toggle' href='javascript:void(0)'>
<span class='zippy toggle-open'>&#9660;&#160;</span>
</a>
<a class='post-count-link' href='https://niemtin007.blogspot.com/2016/'>2016</a>
<span class='post-count' dir='ltr'>(2)</span>
<ul class='hierarchy'>
<li class='archivedate expanded'>
<a class='toggle' href='javascript:void(0)'>
<span class='zippy toggle-open'>&#9660;&#160;</span>
</a>
<a class='post-count-link' href='https://niemtin007.blogspot.com/2016/06/'>June</a>
<span class='post-count' dir='ltr'>(2)</span>
<ul class='posts'>
<li><a href='https://niemtin007.blogspot.com/2016/06/how-to-make-menu-multios-from-clover-custom-entries-windows-macos-linux.html'>[Guide] Sử dụng Clover bootloader để quản lý Multi OS</a></li>
<li><a href='https://niemtin007.blogspot.com/2016/06/how-to-install-parrot-os-uefi-mode-multiboot.html'>[Guide] Hướng dẫn cài đặt Parrot OS trên UEFI mult...</a></li>
</ul>
</li>
</ul>
</li>
</ul>
<ul class='hierarchy'>
<li class='archivedate collapsed'>
<a class='toggle' href='javascript:void(0)'>
<span class='zippy'>

          &#9658;&#160;
        
</span>
</a>
<a class='post-count-link' href='https://niemtin007.blogspot.com/2015/'>2015</a>
<span class='post-count' dir='ltr'>(7)</span>
<ul class='hierarchy'>
<li class='archivedate collapsed'>
<a class='toggle' href='javascript:void(0)'>
<span class='zippy'>

          &#9658;&#160;
        
</span>
</a>
<a class='post-count-link' href='https://niemtin007.blogspot.com/2015/12/'>December</a>
<span class='post-count' dir='ltr'>(1)</span>
</li>
</ul>
<ul class='hierarchy'>
<li class='archivedate collapsed'>
<a class='toggle' href='javascript:void(0)'>
<span class='zippy'>

          &#9658;&#160;
        
</span>
</a>
<a class='post-count-link' href='https://niemtin007.blogspot.com/2015/08/'>August</a>
<span class='post-count' dir='ltr'>(1)</span>
</li>
</ul>
<ul class='hierarchy'>
<li class='archivedate collapsed'>
<a class='toggle' href='javascript:void(0)'>
<span class='zippy'>

          &#9658;&#160;
        
</span>
</a>
<a class='post-count-link' href='https://niemtin007.blogspot.com/2015/07/'>July</a>
<span class='post-count' dir='ltr'>(1)</span>
</li>
</ul>
<ul class='hierarchy'>
<li class='archivedate collapsed'>
<a class='toggle' href='javascript:void(0)'>
<span class='zippy'>

          &#9658;&#160;
        
</span>
</a>
<a class='post-count-link' href='https://niemtin007.blogspot.com/2015/05/'>May</a>
<span class='post-count' dir='ltr'>(1)</span>
</li>
</ul>
<ul class='hierarchy'>
<li class='archivedate collapsed'>
<a class='toggle' href='javascript:void(0)'>
<span class='zippy'>

          &#9658;&#160;
        
</span>
</a>
<a class='post-count-link' href='https://niemtin007.blogspot.com/2015/04/'>April</a>
<span class='post-count' dir='ltr'>(1)</span>
</li>
</ul>
<ul class='hierarchy'>
<li class='archivedate collapsed'>
<a class='toggle' href='javascript:void(0)'>
<span class='zippy'>

          &#9658;&#160;
        
</span>
</a>
<a class='post-count-link' href='https://niemtin007.blogspot.com/2015/02/'>February</a>
<span class='post-count' dir='ltr'>(1)</span>
</li>
</ul>
<ul class='hierarchy'>
<li class='archivedate collapsed'>
<a class='toggle' href='javascript:void(0)'>
<span class='zippy'>

          &#9658;&#160;
        
</span>
</a>
<a class='post-count-link' href='https://niemtin007.blogspot.com/2015/01/'>January</a>
<span class='post-count' dir='ltr'>(1)</span>
</li>
</ul>
</li>
</ul>
<ul class='hierarchy'>
<li class='archivedate collapsed'>
<a class='toggle' href='javascript:void(0)'>
<span class='zippy'>

          &#9658;&#160;
        
</span>
</a>
<a class='post-count-link' href='https://niemtin007.blogspot.com/2014/'>2014</a>
<span class='post-count' dir='ltr'>(3)</span>
<ul class='hierarchy'>
<li class='archivedate collapsed'>
<a class='toggle' href='javascript:void(0)'>
<span class='zippy'>

          &#9658;&#160;
        
</span>
</a>
<a class='post-count-link' href='https://niemtin007.blogspot.com/2014/12/'>December</a>
<span class='post-count' dir='ltr'>(1)</span>
</li>
</ul>
<ul class='hierarchy'>
<li class='archivedate collapsed'>
<a class='toggle' href='javascript:void(0)'>
<span class='zippy'>

          &#9658;&#160;
        
</span>
</a>
<a class='post-count-link' href='https://niemtin007.blogspot.com/2014/10/'>October</a>
<span class='post-count' dir='ltr'>(2)</span>
</li>
</ul>
</li>
</ul>
</div>
</div>
<div class='clear'></div>
</div>
</div></div>
</div><!-- sidebar-wrapper -->
<div class='clear'></div>
</div><!--Div Container-->
</div><!-- outer-wrapper -->
</div><!-- ct-wrapper -->
<div class='container'>
<div class='bondage'>
<div class='bottom-line'>Blog được tạo ra chỉ với mục đích chia sẻ kinh nghiệm. Không thu phí bạn đọc dưới bất kỳ hình thức nào. Chúc các bạn thành công!
</div>
<div class='tride-right'>
<!-- Subscribe Email Form -->
<form action='https://feedburner.google.com/fb/a/mailverify' class='sub-dd' method='post' onsubmit='window.open(&#39;https://feedburner.google.com/fb/a/mailverify?uri=UefiOs;loc=en_US;, &#39;popupwindow&#39;, scrollbars=yes,width=550,height=520&#39;);return true' target='popupwindow'>
<h5>Join Our Newsletter </h5>
<input class='lite' name='email' placeholder='Enter your Email' type='text'/>
<input name='uri' type='hidden' value='niemtin007'/><input name='loc' type='hidden' value='en_US'/>
<input class='buter' type='submit' value='Submit'/>
</form>
<span class='trig-right'>
</span>
</div>
</div>
</div>
<div class='clear'></div>
<div id='footer'>
<div class='container'>
<div class='box2'>
<div class='footer section' id='footer1'><div class='widget Label' data-version='1' id='Label2'>
<h2>Labels</h2>
<div class='widget-content list-label-widget-content'>
<ul>
<li>
<a dir='ltr' href='https://niemtin007.blogspot.com/search/label/Clover'>Clover</a>
</li>
<li>
<a dir='ltr' href='https://niemtin007.blogspot.com/search/label/Hackintosh'>Hackintosh</a>
</li>
<li>
<a dir='ltr' href='https://niemtin007.blogspot.com/search/label/Linux'>Linux</a>
</li>
<li>
<a dir='ltr' href='https://niemtin007.blogspot.com/search/label/multiboot'>multiboot</a>
</li>
<li>
<a dir='ltr' href='https://niemtin007.blogspot.com/search/label/Rescue'>Rescue</a>
</li>
<li>
<a dir='ltr' href='https://niemtin007.blogspot.com/search/label/Windows'>Windows</a>
</li>
</ul>
</div>
</div><div class='widget Navbar' data-version='1' id='Navbar1'><script type="text/javascript">
    function setAttributeOnload(object, attribute, val) {
      if(window.addEventListener) {
        window.addEventListener('load',
          function(){ object[attribute] = val; }, false);
      } else {
        window.attachEvent('onload', function(){ object[attribute] = val; });
      }
    }
  </script>
<div id="navbar-iframe-container"></div>
<script type="text/javascript" src="https://apis.google.com/js/platform.js"></script>
<script type="text/javascript">
      gapi.load("gapi.iframes:gapi.iframes.style.bubble", function() {
        if (gapi.iframes && gapi.iframes.getContext) {
          gapi.iframes.getContext().openChild({
              url: 'https://www.blogger.com/navbar/6617778967066801638?po\x3d*******************\x26origin\x3dhttps://niemtin007.blogspot.com',
              where: document.getElementById("navbar-iframe-container"),
              id: "navbar-iframe"
          });
        }
      });
    </script><script type="text/javascript">
(function() {
var script = document.createElement('script');
script.type = 'text/javascript';
script.src = '//pagead2.googlesyndication.com/pagead/js/google_top_exp.js';
var head = document.getElementsByTagName('head')[0];
if (head) {
head.appendChild(script);
}})();
</script>
</div></div>
<div class='footer section' id='footer2'><div class='widget HTML' data-version='1' id='HTML64'>
<h2 class='title'>Recent Posts</h2>
<div class='widget-content'>
<div id='recent-posts'>
<script type='text/javascript'>//<![CDATA[
var rcp_numposts = 4;
var rcp_snippet_length = 60;
var rcp_info = "no";
var rcp_comment = "comments";
var rcp_disable = "disabled comments";
function recent_posts(json) {
    var dw = "";
    a = location.href;
    y = a.indexOf("?m=0");
    dw += "<ul>";
    for (var i = 0; i < rcp_numposts; i++) {
        var entry = json.feed.entry[i];
        var rcp_posttitle = entry.title.$t;
        if ("content" in entry) var rcp_get_snippet = entry.content.$t;
        else if ("summary" in entry) var rcp_get_snippet = entry.summary.$t;
        else var rcp_get_snippet = "";
        rcp_get_snippet = rcp_get_snippet.replace(/<[^>]*>/g, "");
        if (rcp_get_snippet.length < rcp_snippet_length) var rcp_snippet = rcp_get_snippet;
        else {
            rcp_get_snippet = rcp_get_snippet.substring(0, rcp_snippet_length);
            var space = rcp_get_snippet.lastIndexOf(" ");
            rcp_snippet = rcp_get_snippet.substring(0, space) + "&#133;"
        }
        for (var j = 0; j < entry.link.length; j++) {
            if ("thr$total" in entry) var rcp_commentsNum = entry.thr$total.$t + " " + rcp_comment;
            else rcp_commentsNum = rcp_disable;
            if (entry.link[j].rel == "alternate") {
                var rcp_posturl = entry.link[j].href;
                if (y != -1) rcp_posturl = rcp_posturl + "?m=0";
                var rcp_postdate = entry.published.$t;
                if ("media$thumbnail" in entry) var rcp_thumb = entry.media$thumbnail.url;
                else rcp_thumb = "https://2.bp.blogspot.com/-pAkyABlSI9I/V5Vb3h5bgGI/AAAAAAAAEEg/03XLR_fUHfciuaylJJCi1GxDG2Lw9WqVwCLcB/s320/couper.jpg"
            }
        }
        dw += "<li>";
        dw += '<a href="' + rcp_posturl + '" rel="nofollow" title="' + rcp_posttitle + '"><img alt="' + rcp_posttitle + '" src="' + rcp_thumb + '"/></a>';
        dw += '<div><a href="' + rcp_posturl + '" rel="nofollow">' + rcp_posttitle + "</a></div>";
        if (rcp_info == "yes") dw += "<span>" + rcp_postdate.substring(8, 10) + "/" + rcp_postdate.substring(5, 7) + "/" + rcp_postdate.substring(0, 4) + " - " + rcp_commentsNum + "</span>";
        dw += '<div style="clear:both"></div></li>'
    }
    dw += "</ul>";
    document.getElementById("recent-posts")
        .innerHTML = dw
}
document.write('<script type="text/javascript" src="/feeds/posts/default?alt=json-in-script&max-results=' + rcp_numposts + '&callback=recent_posts"><\/script>');

//]]></script>
</div>
</div>
<div class='clear'></div>
</div></div>
<div class='footer gazer section' id='footer3'><div class='widget ContactForm' data-version='1' id='ContactForm1'>
<h2 class='title'>Contact Form</h2>
<div class='contact-form-widget'>
<div class='form'>
<form name='contact-form'>
<p></p>
Name
<br/>
<input class='contact-form-name' id='ContactForm1_contact-form-name' name='name' size='30' type='text' value=''/>
<p></p>
Email
<span style='font-weight: bolder;'>*</span>
<br/>
<input class='contact-form-email' id='ContactForm1_contact-form-email' name='email' size='30' type='text' value=''/>
<p></p>
Message
<span style='font-weight: bolder;'>*</span>
<br/>
<textarea class='contact-form-email-message' cols='25' id='ContactForm1_contact-form-email-message' name='email-message' rows='5'></textarea>
<p></p>
<input class='contact-form-button contact-form-button-submit' id='ContactForm1_contact-form-submit' type='button' value='Send'/>
<p></p>
<div style='text-align: center; width: 100%'>
<p class='contact-form-error-message' id='ContactForm1_contact-form-error-message'></p>
<p class='contact-form-success-message' id='ContactForm1_contact-form-success-message'></p>
</div>
</form>
</div>
</div>
<div class='clear'></div>
</div></div>
<div class='clear'></div>
<div class='footer-credits'>
<div class='mage2'>
<div class='container'>
<div class='attribution'>&#169; Copyright 2015 <a href='https://niemtin007.blogspot.com/'>UEFI &amp; OS</a>. Designed by <a href='https://www.bloggertheme9.com/' id='credit'>Bloggertheme9</a> | Distributed By <a href='https://gooyaabitemplates.com/' rel='dofollow' target='_blank'>Gooyaabi Templates</a>.

<span class='deen'>
<ul class='Pagemenu'>
<li><a class='home' href='https://niemtin007.blogspot.com/'>Home</a></li>
<!-- Edit footer Page Links -->
<li><a href='https://niemtin007.blogspot.com/2014/10/loi-ngo.html' target='_blank'>About</a></li>
<li><a href='' target='_blank'>Privacy Policy</a></li>
<li><a href='https://www.facebook.com/niemtin007' target='_blank'>Contact me</a></li>
</ul>
<i style='margin-right:8px;'></i> Powered by <a href='https:blogger.com/'>Blogger</a>.</span>
</div>
</div>
</div><!-- mage2 -->
</div><!-- footer-credits -->
</div>
</div><!--Div Container-->
</div><!-- footer -->
<a href='#' id='MD-StoTop' title='back to top'><i class='fa fa-chevron-up'></i></a>
<div id='fb-root'></div>
<script type='text/javascript'>
//<![CDATA[

//Facebook Script
(function(d,s,id){var js,fjs=d.getElementsByTagName(s)[0];if(d.getElementById(id))return;js=d.createElement(s);js.id=id;js.src="//connect.facebook.net/en_US/all.js#xfbml=1";fjs.parentNode.insertBefore(js,fjs)}(document,'script','facebook-jssdk'));

//]]>
</script>
<!-- Page Counter - Edit Number Of Post To Show On Each Page -->
<script type='text/javascript'>
$(document).ready(function(){
var olderLink = $("a.blog-pager-older-link").attr("href");
$("a.blog-pager-older-link").load(olderLink+" .post-title:first", function() {
var olderLinkTitle = $("a.blog-pager-older-link:first").text();
$("a.blog-pager-older-link").text(olderLinkTitle);//rgt
});
var newerLink = $("a.blog-pager-newer-link").attr("href");
$("a.blog-pager-newer-link").load(newerLink+" .post-title:first", function() {
var newerLinkTitle = $("a.blog-pager-newer-link:first").text();
$("a.blog-pager-newer-link").text(newerLinkTitle);
});
});

</script>
<!-- Load Facebook SDK for JavaScript -->
<!-- End -->

<script type="text/javascript" src="https://www.blogger.com/static/v1/widgets/2838643729-widgets.js"></script>
<script type='text/javascript'>
window['__wavt'] = 'AOuZoY6WiTCfXXnTTMCOX1cFh3a-RZEbng:1748941434837';_WidgetManager._Init('//www.blogger.com/rearrange?blogID\x3d6617778967066801638','//niemtin007.blogspot.com/2016/06/how-to-make-menu-multios-from-clover-custom-entries-windows-macos-linux.html','6617778967066801638');
_WidgetManager._SetDataContext([{'name': 'blog', 'data': {'blogId': '6617778967066801638', 'title': 'UEFI \x26amp; OS', 'url': 'https://niemtin007.blogspot.com/2016/06/how-to-make-menu-multios-from-clover-custom-entries-windows-macos-linux.html', 'canonicalUrl': 'https://niemtin007.blogspot.com/2016/06/how-to-make-menu-multios-from-clover-custom-entries-windows-macos-linux.html', 'homepageUrl': 'https://niemtin007.blogspot.com/', 'searchUrl': 'https://niemtin007.blogspot.com/search', 'canonicalHomepageUrl': 'https://niemtin007.blogspot.com/', 'blogspotFaviconUrl': 'https://niemtin007.blogspot.com/favicon.ico', 'bloggerUrl': 'https://www.blogger.com', 'hasCustomDomain': false, 'httpsEnabled': true, 'enabledCommentProfileImages': true, 'gPlusViewType': 'FILTERED_POSTMOD', 'adultContent': false, 'analyticsAccountNumber': '', 'encoding': 'UTF-8', 'locale': 'en', 'localeUnderscoreDelimited': 'en', 'languageDirection': 'ltr', 'isPrivate': false, 'isMobile': false, 'isMobileRequest': false, 'mobileClass': '', 'isPrivateBlog': false, 'isDynamicViewsAvailable': true, 'feedLinks': '\x3clink rel\x3d\x22alternate\x22 type\x3d\x22application/atom+xml\x22 title\x3d\x22UEFI \x26amp; OS - Atom\x22 href\x3d\x22https://niemtin007.blogspot.com/feeds/posts/default\x22 /\x3e\n\x3clink rel\x3d\x22alternate\x22 type\x3d\x22application/rss+xml\x22 title\x3d\x22UEFI \x26amp; OS - RSS\x22 href\x3d\x22https://niemtin007.blogspot.com/feeds/posts/default?alt\x3drss\x22 /\x3e\n\x3clink rel\x3d\x22service.post\x22 type\x3d\x22application/atom+xml\x22 title\x3d\x22UEFI \x26amp; OS - Atom\x22 href\x3d\x22https://www.blogger.com/feeds/6617778967066801638/posts/default\x22 /\x3e\n\n\x3clink rel\x3d\x22alternate\x22 type\x3d\x22application/atom+xml\x22 title\x3d\x22UEFI \x26amp; OS - Atom\x22 href\x3d\x22https://niemtin007.blogspot.com/feeds/*******************/comments/default\x22 /\x3e\n', 'meTag': '', 'adsenseHostId': 'ca-host-pub-****************', 'adsenseHasAds': false, 'adsenseAutoAds': false, 'boqCommentIframeForm': true, 'loginRedirectParam': '', 'view': '', 'dynamicViewsCommentsSrc': '//www.blogblog.com/dynamicviews/4224c15c4e7c9321/js/comments.js', 'dynamicViewsScriptSrc': '//www.blogblog.com/dynamicviews/77c4ed00da3705ef', 'plusOneApiSrc': 'https://apis.google.com/js/platform.js', 'disableGComments': true, 'interstitialAccepted': false, 'sharing': {'platforms': [{'name': 'Get link', 'key': 'link', 'shareMessage': 'Get link', 'target': ''}, {'name': 'Facebook', 'key': 'facebook', 'shareMessage': 'Share to Facebook', 'target': 'facebook'}, {'name': 'BlogThis!', 'key': 'blogThis', 'shareMessage': 'BlogThis!', 'target': 'blog'}, {'name': 'X', 'key': 'twitter', 'shareMessage': 'Share to X', 'target': 'twitter'}, {'name': 'Pinterest', 'key': 'pinterest', 'shareMessage': 'Share to Pinterest', 'target': 'pinterest'}, {'name': 'Email', 'key': 'email', 'shareMessage': 'Email', 'target': 'email'}], 'disableGooglePlus': true, 'googlePlusShareButtonWidth': 0, 'googlePlusBootstrap': '\x3cscript type\x3d\x22text/javascript\x22\x3ewindow.___gcfg \x3d {\x27lang\x27: \x27en\x27};\x3c/script\x3e'}, 'hasCustomJumpLinkMessage': false, 'jumpLinkMessage': 'Read more', 'pageType': 'item', 'postId': '*******************', 'postImageThumbnailUrl': 'https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEhg3SBOwUohbv8QNGG2PoNzENttV9JqyXiKxd0RCQq7smauS3OG17ZGIoYutDo5Q8x3qg7xJdeRxVRDyxLyo0hHv_eNzmZWm1DSBZWEMGQGCLnVMxEYNFWL7C6qp3VhjLznmFkzEpmVvxk/s72-c/clover.png', 'postImageUrl': 'https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEhg3SBOwUohbv8QNGG2PoNzENttV9JqyXiKxd0RCQq7smauS3OG17ZGIoYutDo5Q8x3qg7xJdeRxVRDyxLyo0hHv_eNzmZWm1DSBZWEMGQGCLnVMxEYNFWL7C6qp3VhjLznmFkzEpmVvxk/s1600/clover.png', 'pageName': '[Guide] S\u1eed d\u1ee5ng Clover bootloader \u0111\u1ec3 qu\u1ea3n l\xfd Multi OS', 'pageTitle': 'UEFI \x26amp; OS: [Guide] S\u1eed d\u1ee5ng Clover bootloader \u0111\u1ec3 qu\u1ea3n l\xfd Multi OS', 'metaDescription': 'Custom Entries, Multiboot, Linux, Windows, Mac OS'}}, {'name': 'features', 'data': {}}, {'name': 'messages', 'data': {'edit': 'Edit', 'linkCopiedToClipboard': 'Link copied to clipboard!', 'ok': 'Ok', 'postLink': 'Post Link'}}, {'name': 'template', 'data': {'name': 'custom', 'localizedName': 'Custom', 'isResponsive': false, 'isAlternateRendering': false, 'isCustom': true}}, {'name': 'view', 'data': {'classic': {'name': 'classic', 'url': '?view\x3dclassic'}, 'flipcard': {'name': 'flipcard', 'url': '?view\x3dflipcard'}, 'magazine': {'name': 'magazine', 'url': '?view\x3dmagazine'}, 'mosaic': {'name': 'mosaic', 'url': '?view\x3dmosaic'}, 'sidebar': {'name': 'sidebar', 'url': '?view\x3dsidebar'}, 'snapshot': {'name': 'snapshot', 'url': '?view\x3dsnapshot'}, 'timeslide': {'name': 'timeslide', 'url': '?view\x3dtimeslide'}, 'isMobile': false, 'title': '[Guide] S\u1eed d\u1ee5ng Clover bootloader \u0111\u1ec3 qu\u1ea3n l\xfd Multi OS', 'description': 'Custom Entries, Multiboot, Linux, Windows, Mac OS', 'featuredImage': 'https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEhg3SBOwUohbv8QNGG2PoNzENttV9JqyXiKxd0RCQq7smauS3OG17ZGIoYutDo5Q8x3qg7xJdeRxVRDyxLyo0hHv_eNzmZWm1DSBZWEMGQGCLnVMxEYNFWL7C6qp3VhjLznmFkzEpmVvxk/s1600/clover.png', 'url': 'https://niemtin007.blogspot.com/2016/06/how-to-make-menu-multios-from-clover-custom-entries-windows-macos-linux.html', 'type': 'item', 'isSingleItem': true, 'isMultipleItems': false, 'isError': false, 'isPage': false, 'isPost': true, 'isHomepage': false, 'isArchive': false, 'isLabelSearch': false, 'postId': *******************}}]);
_WidgetManager._RegisterWidget('_HeaderView', new _WidgetInfo('Header1', 'header', document.getElementById('Header1'), {}, 'displayModeFull'));
_WidgetManager._RegisterWidget('_BlogView', new _WidgetInfo('Blog1', 'main', document.getElementById('Blog1'), {'cmtInteractionsEnabled': false, 'lightboxEnabled': true, 'lightboxModuleUrl': 'https://www.blogger.com/static/v1/jsbin/155092491-lbx.js', 'lightboxCssUrl': 'https://www.blogger.com/static/v1/v-css/123180807-lightbox_bundle.css'}, 'displayModeFull'));
_WidgetManager._RegisterWidget('_PopularPostsView', new _WidgetInfo('PopularPosts2', 'sidebar', document.getElementById('PopularPosts2'), {}, 'displayModeFull'));
_WidgetManager._RegisterWidget('_BlogArchiveView', new _WidgetInfo('BlogArchive1', 'sidebar', document.getElementById('BlogArchive1'), {'languageDirection': 'ltr', 'loadingMessage': 'Loading\x26hellip;'}, 'displayModeFull'));
_WidgetManager._RegisterWidget('_LabelView', new _WidgetInfo('Label2', 'footer1', document.getElementById('Label2'), {}, 'displayModeFull'));
_WidgetManager._RegisterWidget('_NavbarView', new _WidgetInfo('Navbar1', 'footer1', document.getElementById('Navbar1'), {}, 'displayModeFull'));
_WidgetManager._RegisterWidget('_HTMLView', new _WidgetInfo('HTML64', 'footer2', document.getElementById('HTML64'), {}, 'displayModeFull'));
_WidgetManager._RegisterWidget('_ContactFormView', new _WidgetInfo('ContactForm1', 'footer3', document.getElementById('ContactForm1'), {'contactFormMessageSendingMsg': 'Sending...', 'contactFormMessageSentMsg': 'Your message has been sent.', 'contactFormMessageNotSentMsg': 'Message could not be sent. Please try again later.', 'contactFormInvalidEmailMsg': 'A valid email address is required.', 'contactFormEmptyMessageMsg': 'Message field cannot be empty.', 'title': 'Contact Form', 'blogId': '6617778967066801638', 'contactFormNameMsg': 'Name', 'contactFormEmailMsg': 'Email', 'contactFormMessageMsg': 'Message', 'contactFormSendMsg': 'Send', 'contactFormToken': 'AOuZoY4T9fBdSxs6nCuxzd9HY7q71MeI5A:1748941434837', 'submitUrl': 'https://www.blogger.com/contact-form.do'}, 'displayModeFull'));
</script>
</body>
</html>