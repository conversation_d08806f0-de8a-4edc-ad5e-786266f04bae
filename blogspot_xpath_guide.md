# Blogspot XPath Extraction Guide

This guide provides comprehensive XPath expressions for extracting data from Blogspot templates, based on analysis of two different Blogspot themes.

## Template Analysis

### Template 1: Clean Shift Theme (blogspot_source.html)
- **Theme**: Clean Shift Blogger Template by <PERSON><PERSON><PERSON>theme9
- **Blog**: UEFI & OS (niemtin007.blogspot.com)
- **Structure**: Traditional Blogger layout with sidebar

### Template 2: Magnews Theme (blogspot2_source.html)
- **Theme**: Magnews v1.1 by <PERSON><PERSON>  
- **Blog**: <PERSON><PERSON><PERSON> (nhasachtinhoc.blogspot.com)
- **Structure**: Modern responsive layout

## Core XPath Expressions

### 1. Blog Title and Header Information

```xpath
# Blog title
//h1[@class='title']/a/span[@itemprop='name']/text()
//h2[@class='title']/a/span[@itemprop='name']/text()

# Blog description
//p[@class='description']/span/text()

# Blog URL
//h1[@class='title']/a/@href
//h2[@class='title']/a/@href

# Header logo/image
//div[@class='header-logo']//img/@src
```

### 2. Post Content Extraction

```xpath
# Post title
//h1[@class='post-title entry-title'][@itemprop='name']/text()
//h1[@class='post-title']/text()
//h3[@class='post-title']/a/text()

# Post URL/permalink
//h1[@class='post-title entry-title']/following-sibling::div//a[@class='timestamp-link']/@href
//h3[@class='post-title']/a/@href

# Post content/body
//div[@class='post-body entry-content']/text()
//div[@class='post-body']//text()

# Post excerpt/summary
//div[@class='post-body entry-content']//text()[position()<=3]

# Post date
//span[@class='published'][@itemprop='datePublished']/@title
//span[@class='published']/text()
//abbr[@class='published']/@title

# Post author
//span[@class='fn'][@itemprop='author']//span[@itemprop='name']/text()
//span[@class='post-author vcard']//span[@itemprop='name']/text()
```

### 3. Post Metadata

```xpath
# Post ID
//div[@class='post hentry']//meta[@itemprop='postId']/@content
//a[starts-with(@name, 'post-')]/@name

# Blog ID  
//meta[@itemprop='blogId']/@content

# Post image/thumbnail
//meta[@itemprop='image_url']/@content
//div[@class='post-body']//img[1]/@src

# Post labels/tags
//span[@class='post-labels']//a/text()
//a[@rel='tag']/text()

# Post comments count
//span[@class='post-comment-link']//text()
//a[contains(@href, '#comments')]/text()
```

### 4. Navigation and Menu

```xpath
# Main navigation menu items
//ul[@class='menu']//li/a/span[@itemprop='name']/text()
//ul[@class='menu']//li/a/@href

# Breadcrumb navigation
//div[@id='breadcrumbs']//li/a/text()
//div[@id='breadcrumbs']//li/a/@href

# Page navigation (older/newer posts)
//a[@class='blog-pager-older-link']/@href
//a[@class='blog-pager-newer-link']/@href
//a[@id='blog-pager-older-link']/@href
//a[@id='blog-pager-newer-link']/@href
```

### 5. Sidebar Widgets

```xpath
# Popular posts
//div[@class='PopularPosts']//li//a/text()
//div[@class='PopularPosts']//li//a/@href
//div[@class='PopularPosts']//li//img/@src

# Recent posts
//div[@id='recent-posts']//li//a/text()
//div[@id='recent-posts']//li//a/@href

# Archive links
//div[@class='BlogArchive']//li/a/text()
//div[@class='BlogArchive']//li/a/@href

# Labels/Categories
//div[@class='Label']//li/a/text()
//div[@class='Label']//li/a/@href

# Widget titles
//div[@class='sidebar']//h2/text()
//div[@class='widget']//h2/text()
```

### 6. Comments Section

```xpath
# Comments container
//div[@id='comments']

# Individual comments
//div[@class='comment-block']//text()

# Comment author
//span[@class='user']/a/text()

# Comment date
//span[@class='datetime']/a/text()
//span[@class='datetime']/a/@title

# Comment content
//div[@class='comment-content']//text()
```

### 7. Social Media and Sharing

```xpath
# Social media links
//div[@class='social-ico']//a/@href
//div[@class='social-ico']//a/@title

# Share buttons
//div[@class='feet-icons']//a/@href
//div[@class='feet-icons']//a/@class
```

### 8. Search Functionality

```xpath
# Search form
//form[@class='search-form']
//div[@class='search']//input[@type='text']

# Search input field
//input[@name='q']
//input[@placeholder='Search...']
```

## Template-Specific XPaths

### Clean Shift Theme Specific

```xpath
# Header phone section
//div[@class='header-phone']//h2/text()
//div[@class='header-phone']//h4/text()

# Featured content
//div[@class='brick']//li//h3/a/text()
//div[@class='brick']//li//img/@src

# Related articles
//div[@id='related-article']//li//a[@class='related-title']/text()
//div[@id='related-article']//li//img/@src
```

### Magnews Theme Specific

```xpath
# Header top section
//div[@class='header-top']
//div[@class='newst']//h4/a/text()

# Content wrapper
//div[@class='content-wrapper']

# Featured posts section
//div[@class='newst-title']//h3/text()
```

## Advanced Extraction Patterns

### 1. Multiple Post Extraction (Blog Homepage)

```xpath
# All post titles on page
//h3[@class='post-title']/a/text() | //h1[@class='post-title']/text()

# All post URLs on page  
//h3[@class='post-title']/a/@href | //h1[@class='post-title']/following-sibling::div//a[@class='timestamp-link']/@href

# All post excerpts
//div[@class='post-body']//text()[string-length(.) > 50][position()<=2]
```

### 2. Conditional Extraction

```xpath
# Post title (try multiple selectors)
(//h1[@class='post-title entry-title'] | //h1[@class='post-title'] | //h3[@class='post-title']/a)[1]/text()

# Post date (multiple formats)
(//span[@class='published']/@title | //span[@class='published']/text() | //abbr[@class='published']/@title)[1]
```

### 3. Text Cleaning and Normalization

```xpath
# Clean post content (remove extra whitespace)
normalize-space(//div[@class='post-body entry-content'])

# Get first paragraph only
//div[@class='post-body']//p[1]/text()

# Extract all text nodes and join
string-join(//div[@class='post-body']//text()[normalize-space()], ' ')
```

## Usage Tips

1. **Test Multiple Selectors**: Blogspot themes vary significantly, so test multiple XPath expressions
2. **Use Fallbacks**: Implement fallback selectors for better reliability
3. **Handle Dynamic Content**: Some content may be loaded via JavaScript
4. **Normalize Text**: Use `normalize-space()` to clean extracted text
5. **Check for Updates**: Blogspot templates can change, so validate XPaths regularly

## Common Issues and Solutions

1. **Empty Results**: Try broader selectors or check for JavaScript-rendered content
2. **Multiple Matches**: Use `[1]` to get the first match or `[last()]` for the last
3. **Namespace Issues**: Blogspot uses various namespaces; adjust accordingly
4. **Encoded Characters**: Handle HTML entities in extracted text
5. **Mobile vs Desktop**: Different templates may be served for mobile devices

This guide covers the most common elements found in Blogspot templates. Adjust the XPaths based on your specific template structure.
