#!/usr/bin/env python3
"""
Test XPath expressions against the provided Blogspot HTML files.
"""

from lxml import html
import json
from pathlib import Path


def load_html_file(filename: str) -> html.HtmlElement:
    """
    Load and parse HTML file.
    
    Args:
        filename: Path to HTML file
        
    Returns:
        Parsed HTML tree
    """
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Handle the special case of blogspot2_source.html which is browser-rendered
        if 'line-number' in content and 'line-content' in content:
            # Extract actual HTML content from the browser view source format
            # This is a simplified extraction - in practice you'd want more robust parsing
            print(f"Note: {filename} appears to be browser-rendered source view")
            return None
        
        return html.fromstring(content)
    except Exception as e:
        print(f"Error loading {filename}: {e}")
        return None


def test_xpath_expressions():
    """
    Test XPath expressions against the provided HTML files.
    """
    files = ['blogspot_source.html', 'blogspot2_source.html']
    
    # XPath test cases
    test_cases = {
        'blog_title': [
            "//h1[@class='title']/a/span[@itemprop='name']/text()",
            "//h2[@class='title']/a/span[@itemprop='name']/text()",
            "//title/text()"
        ],
        'post_title': [
            "//h1[@class='post-title entry-title'][@itemprop='name']/text()",
            "//h1[@class='post-title']/text()",
            "//h3[@class='post-title']/a/text()"
        ],
        'post_content': [
            "//div[@class='post-body entry-content']",
            "//div[@class='post-body']",
            "//div[contains(@class, 'post-content')]"
        ],
        'post_date': [
            "//span[@class='published'][@itemprop='datePublished']/@title",
            "//span[@class='published']/text()",
            "//abbr[@class='published']/@title"
        ],
        'post_author': [
            "//span[@class='fn'][@itemprop='author']//span[@itemprop='name']/text()",
            "//span[@class='post-author vcard']//span[@itemprop='name']/text()"
        ],
        'navigation_menu': [
            "//ul[@class='menu']//li/a/span[@itemprop='name']/text()",
            "//ul[@class='menu']//li/a/text()",
            "//nav//ul//li/a/text()"
        ],
        'sidebar_widgets': [
            "//div[@class='PopularPosts']//li//a/text()",
            "//div[@class='sidebar']//h2/text()",
            "//div[@class='widget']//h2/text()"
        ],
        'meta_description': [
            "//meta[@name='description']/@content",
            "//meta[@property='og:description']/@content"
        ],
        'canonical_url': [
            "//link[@rel='canonical']/@href"
        ],
        'blog_id': [
            "//meta[@itemprop='blogId']/@content"
        ]
    }
    
    results = {}
    
    for filename in files:
        print(f"\n{'='*60}")
        print(f"Testing file: {filename}")
        print('='*60)
        
        tree = load_html_file(filename)
        if tree is None:
            print(f"Skipping {filename} - could not parse")
            continue
        
        file_results = {}
        
        for test_name, xpaths in test_cases.items():
            print(f"\nTesting: {test_name}")
            print("-" * 40)
            
            test_results = []
            
            for i, xpath in enumerate(xpaths, 1):
                try:
                    matches = tree.xpath(xpath)
                    
                    if matches:
                        # Limit output for readability
                        if len(matches) > 3:
                            display_matches = matches[:3] + [f"... and {len(matches)-3} more"]
                        else:
                            display_matches = matches
                        
                        print(f"  XPath {i}: ✓ Found {len(matches)} match(es)")
                        for j, match in enumerate(display_matches):
                            if isinstance(match, str):
                                text = match.strip()[:100]
                            else:
                                text = str(match)[:100]
                            print(f"    [{j+1}] {text}")
                        
                        test_results.append({
                            'xpath': xpath,
                            'matches': len(matches),
                            'first_match': str(matches[0])[:200] if matches else None
                        })
                    else:
                        print(f"  XPath {i}: ✗ No matches")
                        test_results.append({
                            'xpath': xpath,
                            'matches': 0,
                            'first_match': None
                        })
                        
                except Exception as e:
                    print(f"  XPath {i}: ✗ Error - {e}")
                    test_results.append({
                        'xpath': xpath,
                        'matches': 0,
                        'error': str(e)
                    })
            
            file_results[test_name] = test_results
        
        results[filename] = file_results
    
    # Save detailed results to JSON
    with open('xpath_test_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n{'='*60}")
    print("SUMMARY")
    print('='*60)
    
    for filename, file_results in results.items():
        print(f"\nFile: {filename}")
        print("-" * 30)
        
        for test_name, test_results in file_results.items():
            successful_xpaths = sum(1 for r in test_results if r['matches'] > 0)
            total_xpaths = len(test_results)
            
            status = "✓" if successful_xpaths > 0 else "✗"
            print(f"  {status} {test_name}: {successful_xpaths}/{total_xpaths} XPaths successful")
    
    print(f"\nDetailed results saved to: xpath_test_results.json")


def extract_sample_data():
    """
    Extract sample data using the most successful XPath expressions.
    """
    print(f"\n{'='*60}")
    print("EXTRACTING SAMPLE DATA")
    print('='*60)
    
    tree = load_html_file('blogspot_source.html')
    if tree is None:
        print("Could not load blogspot_source.html")
        return
    
    sample_data = {}
    
    # Blog title
    title_result = tree.xpath("//h2[@class='title']/a/span[@itemprop='name']/text()")
    if title_result:
        sample_data['blog_title'] = title_result[0]
    
    # Post title
    post_title_result = tree.xpath("//h1[@class='post-title entry-title'][@itemprop='name']/text()")
    if post_title_result:
        sample_data['post_title'] = post_title_result[0]
    
    # Post author
    author_result = tree.xpath("//span[@class='fn'][@itemprop='author']//span[@itemprop='name']/text()")
    if author_result:
        sample_data['post_author'] = author_result[0]
    
    # Post date
    date_result = tree.xpath("//span[@class='published'][@itemprop='datePublished']/@title")
    if date_result:
        sample_data['post_date'] = date_result[0]
    
    # Navigation menu
    menu_result = tree.xpath("//ul[@class='menu']//li/a/span[@itemprop='name']/text()")
    if menu_result:
        sample_data['navigation_menu'] = menu_result
    
    # Meta description
    desc_result = tree.xpath("//meta[@name='description']/@content")
    if desc_result:
        sample_data['meta_description'] = desc_result[0]
    
    # Canonical URL
    url_result = tree.xpath("//link[@rel='canonical']/@href")
    if url_result:
        sample_data['canonical_url'] = url_result[0]
    
    print("Extracted Sample Data:")
    print("-" * 30)
    for key, value in sample_data.items():
        if isinstance(value, list):
            print(f"{key}: {len(value)} items")
            for i, item in enumerate(value[:3], 1):
                print(f"  {i}. {item}")
            if len(value) > 3:
                print(f"  ... and {len(value)-3} more")
        else:
            print(f"{key}: {value}")
    
    # Save sample data
    with open('sample_extracted_data.json', 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, indent=2, ensure_ascii=False)
    
    print(f"\nSample data saved to: sample_extracted_data.json")


def main():
    """
    Main function to run all tests.
    """
    print("Blogspot XPath Expression Tester")
    print("=" * 60)
    
    # Check if HTML files exist
    files_exist = []
    for filename in ['blogspot_source.html', 'blogspot2_source.html']:
        if Path(filename).exists():
            files_exist.append(filename)
            print(f"✓ Found: {filename}")
        else:
            print(f"✗ Missing: {filename}")
    
    if not files_exist:
        print("\nNo HTML files found. Please ensure the files are in the current directory.")
        return
    
    # Run tests
    test_xpath_expressions()
    
    # Extract sample data
    extract_sample_data()
    
    print(f"\n{'='*60}")
    print("Testing completed!")
    print("Check the generated JSON files for detailed results.")
    print('='*60)


if __name__ == "__main__":
    main()
